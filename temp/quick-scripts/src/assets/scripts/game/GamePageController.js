"use strict";
cc._RF.push(module, 'ae7d7j8qCJHEr/tVZKmu8hm', 'GamePageController');
// scripts/game/GamePageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var CongratsDialogController_1 = require("./CongratsDialogController");
var GameScoreController_1 = require("./GameScoreController");
var ChessBoardController_1 = require("./Chess/ChessBoardController");
var HexChessBoardController_1 = require("./Chess/HexChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageController = /** @class */ (function (_super) {
    __extends(GamePageController, _super);
    function GamePageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBtnBack = null; //返回按钮
        _this.timeLabel = null; // 计时器显示标签
        _this.mineCountLabel = null; // 炸弹数量显示标签
        _this.squareMapNode = null; // 方形地图节点 (mapType = 0)
        _this.hexMapNode = null; // 六边形地图节点 (mapType = 1)
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.congratsDialogController = null; //结算弹窗
        _this.gameScoreController = null; //分数控制器
        _this.chessBoardController = null; //方形棋盘控制器
        _this.hexChessBoardController = null; //六边形棋盘控制器
        _this.isLeaveGameDialogShow = false; //是否显示退出游戏的弹窗
        _this.isCongratsDialog = false; //是否显示结算的弹窗
        // 计时器相关属性
        _this.countdownInterval = null; // 倒计时定时器ID
        _this.currentCountdown = 0; // 当前倒计时秒数
        _this.currentRoundNumber = 0; // 当前回合编号
        // 游戏状态管理
        _this.canOperate = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）
        _this.gameStatus = 0; // 游戏状态
        _this.hasOperatedThisRound = false; // 本回合是否已经操作过
        // 游戏数据
        _this.currentMapType = 0; // 当前地图类型 0-方形地图，1-六边形地图
        _this.currentMineCount = 0; // 当前炸弹数量
        return _this;
        // update (dt) {}
    }
    GamePageController.prototype.onLoad = function () {
        var _this = this;
        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.timeLabel) {
            // 根据场景结构查找time_label节点
            var timeBgNode = cc.find('Canvas/time_bg');
            if (timeBgNode) {
                var timeLabelNode = timeBgNode.getChildByName('time_label');
                if (timeLabelNode) {
                    this.timeLabel = timeLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.mineCountLabel) {
            // 根据场景结构查找mine_count_label节点
            var mineCountBgNode = cc.find('Canvas/mine_count_bg');
            if (mineCountBgNode) {
                var mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');
                if (mineCountLabelNode) {
                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 将测试方法暴露到全局，方便调试
        window.testGameReset = function () {
            _this.testReset();
        };
        // 暴露 GamePageController 实例到全局
        window.gamePageController = this;
    };
    GamePageController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
            _this.isLeaveGameDialogShow = true;
            _this.leaveDialogController.show(1, function () {
                _this.isLeaveGameDialogShow = false;
            });
        });
        // 监听棋盘点击事件
        if (this.chessBoardController) {
            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);
        }
        // 监听六边形棋盘点击事件
        if (this.hexChessBoardController) {
            this.hexChessBoardController.node.on('hex-chess-board-click', this.onHexChessBoardClick, this);
        }
    };
    /**
     * 处理棋盘点击事件
     * @param event 事件数据 {x: number, y: number, action: number}
     */
    GamePageController.prototype.onChessBoardClick = function (event) {
        var _a = event.detail || event, x = _a.x, y = _a.y, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送点击操作
        this.sendClickBlock(x, y, action);
        // 操作有效，通知棋盘生成预制体
        if (this.chessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    /**
     * 处理六边形棋盘点击事件
     * @param event 事件数据 {q: number, r: number, action: number}
     */
    GamePageController.prototype.onHexChessBoardClick = function (event) {
        var _a = event.detail || event, q = _a.q, r = _a.r, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送六边形点击操作（需要将六边形坐标转换为服务器期望的格式）
        this.sendHexClickBlock(q, r, action);
        // 操作有效，通知六边形棋盘生成预制体
        if (this.hexChessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    //结算
    GamePageController.prototype.setCongratsDialog = function (noticeSettlement) {
        var _this = this;
        this.setCongrats(noticeSettlement);
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        this.isCongratsDialog = true;
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, function () {
            _this.isCongratsDialog = false;
        });
    };
    GamePageController.prototype.onDisable = function () {
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            this.congratsDialogController.hide();
        }
        // 清理计时器
        this.clearCountdownTimer();
    };
    //结算
    GamePageController.prototype.setCongrats = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            AudioManager_1.AudioManager.winAudio(); // 默认播放胜利音效
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (userList[index].rank === 1) { //判断自己是不是第一名
                AudioManager_1.AudioManager.winAudio();
            }
            else {
                AudioManager_1.AudioManager.loseAudio();
            }
        }
        else {
            AudioManager_1.AudioManager.winAudio();
        }
    };
    // 处理游戏开始通知，获取炸弹数量和地图类型
    GamePageController.prototype.onGameStart = function (data) {
        // 保存地图类型
        this.currentMapType = data.mapType || 0;
        // 根据地图类型重置对应的棋盘控制器
        if (this.currentMapType === 0) {
            // 方形地图
            if (this.chessBoardController) {
                this.chessBoardController.resetGameScene();
            }
            else {
                console.error("❌ chessBoardController 不存在！");
            }
        }
        else if (this.currentMapType === 1) {
            // 六边形地图
            if (this.hexChessBoardController) {
                this.hexChessBoardController.resetGameScene();
                // 忽略服务器的 validHexCoords，使用前端节点坐标
                this.hexChessBoardController.setValidHexCoords([]); // 传入空数组，会被忽略
            }
            else {
                console.error("❌ hexChessBoardController 不存在！");
            }
        }
        // 重置游戏状态
        this.canOperate = false;
        this.hasOperatedThisRound = false;
        this.currentRoundNumber = 0;
        this.currentCountdown = 0;
        this.gameStatus = 0;
        // 根据地图类型获取炸弹数量
        if (data.mapType === 0 && data.mapConfig) {
            // 方形地图
            this.currentMineCount = data.mapConfig.mineCount || 13;
        }
        else if (data.mapType === 1) {
            // 六边形地图，根据前端节点数量计算炸弹数量
            if (this.hexChessBoardController) {
                this.currentMineCount = this.hexChessBoardController.getRecommendedMineCount();
            }
            else {
                this.currentMineCount = 15; // 备用固定值
            }
        }
        else {
            // 默认值
            this.currentMineCount = 13;
        }
        // 更新炸弹数UI
        this.updateMineCountDisplay(this.currentMineCount);
        // 根据地图类型控制地图节点的显示与隐藏
        this.switchMapDisplay(this.currentMapType);
        // 初始化分数界面（使用后端传回来的真实数据）
        if (this.gameScoreController) {
            this.gameScoreController.initializeScoreView();
        }
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    GamePageController.prototype.testReset = function () {
        if (this.chessBoardController) {
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
    };
    // 处理扫雷回合开始通知
    GamePageController.prototype.onNoticeRoundStart = function (data) {
        this.currentRoundNumber = data.roundNumber || 1;
        this.currentCountdown = data.countDown || 25;
        this.gameStatus = data.gameStatus || 0;
        // 新回合开始，重置操作状态
        this.canOperate = true;
        this.hasOperatedThisRound = false;
        // 清理棋盘上的所有玩家预制体
        if (this.chessBoardController) {
            this.chessBoardController.clearAllPlayerNodes();
        }
        // 开始倒计时
        this.startCountdown(this.currentCountdown);
    };
    // 处理扫雷操作展示通知
    GamePageController.prototype.onNoticeActionDisplay = function (data) {
        var _this = this;
        // 进入展示阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 0;
        // 根据countDown重置倒计时为5秒
        this.currentCountdown = data.countDown || 5;
        this.updateCountdownDisplay(this.currentCountdown);
        this.startCountdown(this.currentCountdown);
        // 更新剩余炸弹数量显示
        if (data.remainingMines !== undefined) {
            this.updateMineCountDisplay(data.remainingMines);
        }
        // 在棋盘上显示所有玩家的操作
        this.displayPlayerActions(data.playerActions, data.playerTotalScores);
        // 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）
        // 使用setTimeout作为备选方案
        setTimeout(function () {
            _this.updateBoardAfterActions(data);
        }, 1000);
        // 同时也使用scheduleOnce
        this.scheduleOnce(this.delayedUpdateBoard.bind(this, data), 1.0);
    };
    /**
     * 延迟更新棋盘的回调方法
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.delayedUpdateBoard = function (data) {
        this.updateBoardAfterActions(data);
    };
    /**
     * 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.updateBoardAfterActions = function (data) {
        var _this = this;
        // 第一步：先显示分数动画（在头像还存在的时候）
        if (data.playerTotalScores) {
            this.displayPlayerScoreAnimationsAndUpdateTotalScores(data.playerActions, data.playerTotalScores);
        }
        // 第二步：延迟1.5秒后让所有头像消失（给分数动画足够的时间）
        this.scheduleOnce(function () {
            _this.hideAllAvatars(data.playerActions, function () {
                // 头像消失完成后，执行后续操作
                // 第三步：处理每个玩家的操作结果
                // 先按位置分组，处理同一位置有多个操作的情况
                var processedPositions = new Set();
                data.playerActions.forEach(function (action) {
                    var positionKey = action.x + "," + action.y;
                    // 如果这个位置已经处理过，跳过
                    if (processedPositions.has(positionKey)) {
                        return;
                    }
                    // 查找同一位置的所有操作
                    var samePositionActions = data.playerActions.filter(function (a) {
                        return a.x === action.x && a.y === action.y;
                    });
                    // 处理同一位置的操作结果（不再显示分数，因为已经在第一步显示了）
                    _this.processPositionResult(action.x, action.y, samePositionActions);
                    // 标记这个位置已处理
                    processedPositions.add(positionKey);
                });
                // 第四步：处理连锁展开结果
                if (data.floodFillResults && data.floodFillResults.length > 0) {
                    data.floodFillResults.forEach(function (floodFill) {
                        _this.processFloodFillResult(floodFill);
                    });
                }
            });
        }, 1.5);
    };
    /**
     * 让所有头像消失（支持方形地图和六边形地图）
     * @param playerActions 玩家操作列表
     * @param onComplete 完成回调
     */
    GamePageController.prototype.hideAllAvatars = function (playerActions, onComplete) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图：直接调用一次头像删除，不区分位置
            this.chessBoardController.hideAvatarsAtPosition(0, 0, function () {
                onComplete();
            });
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图：直接调用方法（已经编译成功）
            this.hexChessBoardController.hideAllHexAvatars(function () {
                onComplete();
            });
        }
        else {
            // 没有可用的控制器，直接执行回调
            console.warn("没有可用的棋盘控制器，跳过头像消失动画");
            onComplete();
        }
    };
    /**
     * 处理同一位置的多个操作结果
     * @param x 格子x坐标（方形地图）或q坐标（六边形地图）
     * @param y 格子y坐标（方形地图）或r坐标（六边形地图）
     * @param actions 该位置的所有操作
     */
    GamePageController.prototype.processPositionResult = function (x, y, actions) {
        var _a, _b;
        // 根据地图类型删除该位置的格子
        if (this.currentMapType === 0) {
            // 方形地图
            this.chessBoardController.removeGridAt(x, y);
        }
        else if (this.currentMapType === 1) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            if (this.hexChessBoardController) {
                this.hexChessBoardController.hideHexGridAt(x, y);
            }
        }
        // 检查是否有地雷被点击（action=1且result="mine"）
        var mineClickAction = actions.find(function (action) {
            return action.action === 1 && action.result === "mine";
        });
        if (mineClickAction) {
            // 如果有地雷被点击，直接显示炸弹，不管是否有标记
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = mineClickAction.userId === currentUserId;
            // 根据地图类型调用对应的方法
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBoomPrefab(x, y, isCurrentUser);
                }
            }
            return;
        }
        // 如果没有地雷被点击，按原逻辑处理第一个操作的结果
        var firstAction = actions[0];
        var result = firstAction.result;
        if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBiaojiPrefab(x, y);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBiaojiPrefab(x, y);
                }
            }
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.updateHexNeighborMinesDisplay(x, y, result);
                }
            }
        }
    };
    /**
     * 处理单个玩家操作结果（保留原方法以防其他地方调用）
     * @param action 玩家操作数据
     */
    GamePageController.prototype.processPlayerActionResult = function (action) {
        var _a, _b;
        var x = action.x;
        var y = action.y;
        var result = action.result;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 根据结果生成相应的预制体
        if (result === "mine") {
            // 地雷：生成boom预制体
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = action.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
        }
        else if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理连锁展开结果
     * @param floodFill 连锁展开数据
     */
    GamePageController.prototype.processFloodFillResult = function (floodFill) {
        var _this = this;
        // 为每个连锁格子播放消失动画
        floodFill.revealedBlocks.forEach(function (block, index) {
            // 延迟播放动画，创造连锁效果
            _this.scheduleOnce(function () {
                if (_this.currentMapType === 0) {
                    // 方形地图
                    _this.chessBoardController.playGridDisappearAnimation(block.x, block.y, block.neighborMines);
                }
                else if (_this.currentMapType === 1) {
                    // 六边形地图，x实际是q坐标，y实际是r坐标
                    if (_this.hexChessBoardController) {
                        // 先隐藏格子
                        _this.hexChessBoardController.hideHexGridAt(block.x, block.y);
                        // 然后显示数字（如果有的话）
                        if (block.neighborMines > 0) {
                            _this.hexChessBoardController.updateHexNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                        }
                    }
                }
            }, index * 0.1); // 每个格子间隔0.1秒
        });
    };
    // 处理扫雷回合结束通知
    GamePageController.prototype.onNoticeRoundEnd = function (data) {
        // 进入回合结束阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 1;
        // 不再处理倒计时，让客户端自然倒计时到0，方便展示54321
        // 处理玩家分数动画和头像显示
        if (data.playerResults && data.playerResults.length > 0) {
            this.displayPlayerScoreAnimations(data.playerResults);
            // 如果本回合我没有操作，根据后端消息生成我的头像
            this.handleMyAvatarIfNotOperated(data.playerResults);
        }
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayers();
        }
    };
    /**
     * 在棋盘上显示所有玩家的操作
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerActions = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 检查是否有可用的棋盘控制器
        var hasSquareBoard = this.chessBoardController && this.currentMapType === 0;
        var hasHexBoard = this.hexChessBoardController && this.currentMapType === 1;
        if ((!hasSquareBoard && !hasHexBoard) || !playerActions || playerActions.length === 0) {
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 注意：分数动画已经在updateBoardAfterActions的第一步显示了，这里不再重复显示
        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像
        var myAction = playerActions.find(function (action) { return action.userId === currentUserId; });
        var shouldDisplayMyAvatar = false;
        if (!this.hasOperatedThisRound && myAction) {
            shouldDisplayMyAvatar = true;
            // 生成我的头像
            var withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.placePlayerOnHexGrid(myAction.x, myAction.y, withFlag);
                }
            }
        }
        // 过滤掉自己的操作，只显示其他玩家的操作
        var otherPlayersActions = playerActions.filter(function (action) { return action.userId !== currentUserId; });
        if (otherPlayersActions.length === 0) {
            return;
        }
        // 按位置分组其他玩家的操作
        var positionGroups = this.groupActionsByPosition(otherPlayersActions);
        // 为每个位置生成预制体
        positionGroups.forEach(function (actions, positionKey) {
            var _a = positionKey.split(',').map(Number), x = _a[0], y = _a[1];
            if (_this.currentMapType === 0) {
                // 方形地图
                _this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 直接调用方法（已经编译成功）
                    _this.hexChessBoardController.displayOtherPlayersAtHexPosition(x, y, actions);
                }
            }
        });
    };
    /**
     * 按位置分组玩家操作
     * @param playerActions 玩家操作列表
     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value
     */
    GamePageController.prototype.groupActionsByPosition = function (playerActions) {
        var groups = new Map();
        for (var _i = 0, playerActions_1 = playerActions; _i < playerActions_1.length; _i++) {
            var action = playerActions_1[_i];
            var positionKey = action.x + "," + action.y;
            if (!groups.has(positionKey)) {
                groups.set(positionKey, []);
            }
            groups.get(positionKey).push(action);
        }
        return groups;
    };
    /**
     * 显示玩家分数动画
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.displayPlayerScoreAnimations = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示分数动画
        playerResults.forEach(function (result, index) {
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                _this.showPlayerScoreAnimation(result, currentUserId);
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画
     * @param result 玩家回合结果
     * @param currentUserId 当前用户ID
     */
    GamePageController.prototype.showPlayerScoreAnimation = function (result, currentUserId) {
        var isMyself = result.userId === currentUserId;
        if (isMyself) {
            // 自己的分数动画：在player_game_pfb里只显示本回合得分
            this.showMyScoreAnimation(result);
        }
        else {
            // 其他人的分数动画：根据isFirstChoice决定显示逻辑
            this.showOtherPlayerScoreAnimation(result);
        }
    };
    /**
     * 显示自己的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showMyScoreAnimation = function (result) {
        // 在棋盘上的头像预制体中显示本回合得分
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
        }
        // 在player_score_pfb中显示分数动画
        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);
    };
    /**
     * 显示其他玩家的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showOtherPlayerScoreAnimation = function (result) {
        if (result.isFirstChoice) {
            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分
            this.showFirstChoiceScoreAnimation(result.userId, result.score);
        }
        else {
            // 其他人非先手：正常显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb中显示分数动画
            this.showScoreAnimationInScorePanel(result.userId, result.score, false);
        }
    };
    /**
     * 在分数面板中显示分数动画
     * @param userId 用户ID
     * @param score 本回合得分
     * @param isFirstChoice 是否为先手
     */
    GamePageController.prototype.showScoreAnimationInScorePanel = function (userId, score, isFirstChoice) {
        // 这里需要找到对应的PlayerScoreController并调用分数动画
        // 由于没有直接的引用，这里先用日志记录
        // TODO: 实现在player_score_pfb中显示分数动画的逻辑
        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法
    };
    /**
     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）
     * @param userId 用户ID
     * @param score 本回合得分
     */
    GamePageController.prototype.showFirstChoiceScoreAnimation = function (userId, score) {
        var _this = this;
        // 先显示+1的先手奖励
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, 1, true);
        }, 0.1);
        // 再显示本回合得分
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, score, false);
        }, 1.2);
        // 最后更新总分
        this.scheduleOnce(function () {
            _this.updatePlayerTotalScore(userId, score + 1);
        }, 2.4);
    };
    /**
     * 更新玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScore = function (userId, totalScore) {
        // TODO: 实现更新玩家总分的逻辑
        // 需要更新GlobalBean中的用户数据，并刷新UI显示
    };
    /**
     * 如果本回合我没有操作，根据后端消息生成我的头像
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.handleMyAvatarIfNotOperated = function (playerResults) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 检查本回合是否进行了操作
        if (this.hasOperatedThisRound) {
            return;
        }
        // 查找我的操作结果
        var myResult = playerResults.find(function (result) { return result.userId === currentUserId; });
        if (!myResult) {
            return;
        }
        // 根据后端消息生成我的头像
        if (this.chessBoardController) {
            // 根据操作类型决定是否显示旗子
            var withFlag = (myResult.action === 2); // action=2表示标记操作，显示旗子
            // 生成我的头像预制体
            this.chessBoardController.placePlayerOnGrid(myResult.x, myResult.y, withFlag);
        }
    };
    // 发送点击方块消息
    GamePageController.prototype.sendClickBlock = function (x, y, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 发送六边形点击方块消息
    GamePageController.prototype.sendHexClickBlock = function (q, r, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 根据当前地图类型决定发送格式
        if (this.currentMapType === 1) {
            // 六边形地图：使用六边形坐标格式
            var hexClickData = {
                q: q,
                r: r,
                action: action // 1=挖掘方块，2=标记/取消标记地雷
            };
            // 注意：这里仍然使用 MsgTypeClickBlock，但数据格式不同
            // 后端应该根据当前房间的 mapType 来解析不同的坐标格式
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, hexClickData);
        }
        else {
            // 方形地图：转换为x,y坐标（备用方案）
            var clickData = {
                x: q,
                y: r,
                action: action
            };
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        }
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 检查是否可以操作
    GamePageController.prototype.isCanOperate = function () {
        return this.canOperate && !this.hasOperatedThisRound;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GamePageController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 转发给GameScoreController处理所有玩家的分数更新和加分动画
        if (this.gameScoreController) {
            this.gameScoreController.onNoticeFirstChoiceBonus(data);
        }
        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var isMyself = (data.userId === currentUserId);
        if (isMyself) {
            // 更新player_game_pfb中的change_score显示
            this.updatePlayerGameScore(data.userId, data.bonusScore);
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GamePageController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        // 调用ChessBoardController显示加分效果
        if (this.chessBoardController) {
            this.chessBoardController.showPlayerGameScore(userId, bonusScore);
        }
        else {
            console.warn("ChessBoardController未设置，无法显示player_game_pfb加分效果");
        }
    };
    // 获取当前地图类型
    GamePageController.prototype.getCurrentMapType = function () {
        return this.currentMapType;
    };
    // 获取当前炸弹数量
    GamePageController.prototype.getCurrentMineCount = function () {
        return this.currentMineCount;
    };
    // 获取当前回合操作状态（用于调试）
    GamePageController.prototype.getCurrentRoundStatus = function () {
        return {
            roundNumber: this.currentRoundNumber,
            canOperate: this.canOperate,
            hasOperated: this.hasOperatedThisRound
        };
    };
    // 开始倒计时
    GamePageController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 清除之前的计时器
        this.clearCountdownTimer();
        var remainingSeconds = seconds;
        this.updateCountdownDisplay(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            _this.updateCountdownDisplay(remainingSeconds);
            if (remainingSeconds <= 0) {
                _this.clearCountdownTimer();
            }
        }, 1000);
    };
    // 更新倒计时显示
    GamePageController.prototype.updateCountdownDisplay = function (seconds) {
        if (this.timeLabel) {
            this.timeLabel.string = seconds + "s"; // 显示数字加s：5s, 4s, 3s, 2s, 1s, 0s
        }
        this.currentCountdown = seconds;
    };
    // 更新炸弹数显示
    GamePageController.prototype.updateMineCountDisplay = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = "" + mineCount;
        }
    };
    // 根据地图类型切换地图显示
    GamePageController.prototype.switchMapDisplay = function (mapType) {
        // 先隐藏所有地图
        this.hideAllMaps();
        // 根据地图类型显示对应的地图
        if (mapType === 0) {
            this.showSquareMap();
        }
        else if (mapType === 1) {
            this.showHexMap();
        }
        else {
            console.warn("\u672A\u77E5\u7684\u5730\u56FE\u7C7B\u578B: " + mapType + "\uFF0C\u9ED8\u8BA4\u663E\u793A\u65B9\u5F62\u5730\u56FE");
            this.showSquareMap();
        }
    };
    // 显示方形地图
    GamePageController.prototype.showSquareMap = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = true;
        }
        else {
            console.warn('方形地图节点未挂载');
        }
    };
    // 显示六边形地图
    GamePageController.prototype.showHexMap = function () {
        if (this.hexMapNode) {
            this.hexMapNode.active = true;
        }
        else {
            console.warn('六边形地图节点未挂载');
        }
    };
    // 隐藏所有地图
    GamePageController.prototype.hideAllMaps = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = false;
        }
        if (this.hexMapNode) {
            this.hexMapNode.active = false;
        }
    };
    // 清除倒计时定时器
    GamePageController.prototype.clearCountdownTimer = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    /**
     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerScoreAnimationsAndUpdateTotalScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        var isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;
        // 如果我不是先手，先为先手玩家在分数面板显示+1
        if (!isCurrentUserFirstChoice && firstChoicePlayer) {
            var firstChoiceUserIndex_1 = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex_1 !== -1) {
                // 0.1秒后显示先手+1
                this.scheduleOnce(function () {
                    _this.showScoreInScorePanel(firstChoiceUserIndex_1, 1);
                }, 0.1);
            }
        }
        // 为每个玩家显示分数动画和更新总分
        playerActions.forEach(function (action, index) {
            var totalScore = playerTotalScores[action.userId] || 0;
            var isFirstChoice = action.isFirstChoice;
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                if (isFirstChoice) {
                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）
                    _this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);
                }
                else {
                    // 非先手玩家：直接显示本回合分数
                    _this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);
                }
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showPlayerScoreAnimationAndUpdateTotal = function (action, currentUserId, totalScore) {
        var _this = this;
        var isMyself = action.userId === currentUserId;
        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）
        if (this.gameScoreController) {
            // 找到用户索引
            var userIndex = this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                // 在分数面板显示加减分效果
                this.showScoreInScorePanel(userIndex, action.score);
            }
        }
        // 2. 更新总分（参考先手加分的updatePlayerScore）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                // 更新全局数据中的总分
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 1.2);
        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 更新全局数据中的玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScoreInGlobalData = function (userId, totalScore) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家总分");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1) {
            users[userIndex].score = totalScore;
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6: userId=" + userId);
        }
    };
    /**
     * 查找用户索引
     * @param userId 用户ID
     * @returns 用户索引，找不到返回-1
     */
    GamePageController.prototype.findUserIndex = function (userId) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法查找用户索引");
            return -1;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        return users.findIndex(function (user) { return user.userId === userId; });
    };
    /**
     * 在玩家头像上显示加减分
     * @param userId 用户ID
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreOnPlayerAvatar = function (userId, score) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, score);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, score);
        }
        else {
            console.warn("没有可用的棋盘控制器，无法显示头像分数");
        }
    };
    /**
     * 在分数面板显示加减分效果
     * @param userIndex 用户索引
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreInScorePanel = function (userIndex, score) {
        if (!this.gameScoreController) {
            console.warn("gameScoreController 不存在，无法在分数面板显示分数");
            return;
        }
        // 获取对应的PlayerScoreController
        var playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);
        if (playerScoreController) {
            // 显示加减分效果
            if (score > 0) {
                playerScoreController.showAddScore(score);
            }
            else if (score < 0) {
                playerScoreController.showSubScore(Math.abs(score));
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u7528\u6237\u7D22\u5F15 " + userIndex + " \u5BF9\u5E94\u7684PlayerScoreController");
        }
    };
    /**
     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showFirstChoicePlayerScoreAnimation = function (action, currentUserId, totalScore) {
        var _this = this;
        var userIndex = this.findUserIndex(action.userId);
        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）
        this.scheduleOnce(function () {
            // 分数面板显示本回合分数（+1已经在前面显示过了）
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        }, 1.2);
        // 第二步：更新总分（2.4秒）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 2.4);
        // 第三步：在player_game_pfb中显示本回合的加减分（与非先手玩家同步）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    GamePageController.prototype.onDestroy = function () {
        // 移除棋盘点击事件监听
        if (this.chessBoardController) {
            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "timeLabel", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "squareMapNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "hexMapNode", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], GamePageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(CongratsDialogController_1.default)
    ], GamePageController.prototype, "congratsDialogController", void 0);
    __decorate([
        property(GameScoreController_1.default)
    ], GamePageController.prototype, "gameScoreController", void 0);
    __decorate([
        property(ChessBoardController_1.default)
    ], GamePageController.prototype, "chessBoardController", void 0);
    __decorate([
        property(HexChessBoardController_1.default)
    ], GamePageController.prototype, "hexChessBoardController", void 0);
    GamePageController = __decorate([
        ccclass
    ], GamePageController);
    return GamePageController;
}(cc.Component));
exports.default = GamePageController;

cc._RF.pop();