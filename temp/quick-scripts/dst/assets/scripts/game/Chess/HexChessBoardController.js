
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd9f2eirVKNNorhz2LDmkG2T', 'HexChessBoardController');
// scripts/game/Chess/HexChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexChessBoardController = /** @class */ (function (_super) {
    __extends(HexChessBoardController, _super);
    function HexChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null;
        _this.boardNode = null; // 棋盘节点
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        return _this;
    }
    HexChessBoardController.prototype.onLoad = function () {
    };
    HexChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.boardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var coords = this_1.parseHexCoordinateFromName(child.name);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(child.name);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                }
            }
        }
    };
    // 从节点名称解析六边形坐标
    HexChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 从位置计算六边形坐标（近似）
    HexChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 六边形坐标转换（从像素坐标到六边形坐标）
        var x = pos.x;
        var y = pos.y;
        // 使用六边形坐标转换公式
        var q = Math.round((Math.sqrt(3) / 3 * x - 1 / 3 * y) / this.HEX_SIZE);
        var r = Math.round((2 / 3 * y) / this.HEX_SIZE);
        // 检查是否为有效坐标
        if (this.isValidHexCoordinate(q, r)) {
            return { q: q, r: r };
        }
        return null;
    };
    // 计算六边形预制体的生成位置（直接使用您提供的格子中心坐标）
    HexChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        // 您提供的精确格子中心坐标
        var exactCoords = new Map();
        // 更新后的基准点坐标（与rowData保持一致）
        exactCoords.set("0,0", cc.v2(-300, -258)); // r=0行基准点
        exactCoords.set("1,-1", cc.v2(-258, -184)); // r=-1行基准点
        exactCoords.set("1,-2", cc.v2(-300, -108)); // r=-2行基准点
        exactCoords.set("2,-3", cc.v2(-258, -36)); // r=-3行基准点
        exactCoords.set("2,-4", cc.v2(-300, 37)); // r=-4行基准点
        exactCoords.set("3,-5", cc.v2(-258, 110)); // r=-5行基准点
        exactCoords.set("3,-6", cc.v2(-300, 185)); // r=-6行基准点
        exactCoords.set("4,-7", cc.v2(-258, 260)); // r=-7行基准点
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (exactCoords.has(key)) {
            var pos = exactCoords.get(key);
            // 如果是单人头像预制体，往左上偏移一点点
            if (isPlayerAvatar) {
                return cc.v2(pos.x + 5, pos.y - 12);
            }
            return pos;
        }
        // 对于其他坐标，使用基于您提供的精确坐标数据进行计算
        // 定义每一行的数据：使用统一步长86，保证美观整齐
        var UNIFORM_STEP_X = 86; // 统一的x方向步长
        var rowData = new Map();
        // 基于您提供的更新数据，使用统一步长86
        rowData.set(0, { baseQ: 0, baseX: -300, y: -258 }); // r=0行：基准点(0,0) → (-300, -258)
        rowData.set(-1, { baseQ: 1, baseX: -258, y: -184 }); // r=-1行：基准点(1,-1) → (-258, -184)
        rowData.set(-2, { baseQ: 1, baseX: -300, y: -108 }); // r=-2行：基准点(1,-2) → (-300, -108)
        rowData.set(-3, { baseQ: 2, baseX: -258, y: -36 }); // r=-3行：基准点(2,-3) → (-258, -36)
        rowData.set(-4, { baseQ: 2, baseX: -300, y: 37 }); // r=-4行：基准点(2,-4) → (-300, 37)
        rowData.set(-5, { baseQ: 3, baseX: -258, y: 110 }); // r=-5行：基准点(3,-5) → (-258, 110)
        rowData.set(-6, { baseQ: 3, baseX: -300, y: 185 }); // r=-6行：基准点(3,-6) → (-300, 185)
        rowData.set(-7, { baseQ: 4, baseX: -258, y: 260 }); // r=-7行：基准点(4,-7) → (-258, 260)
        // 计算基础位置
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (rowData.has(r)) {
            var data = rowData.get(r);
            x = data.baseX + (q - data.baseQ) * UNIFORM_STEP_X;
            y = data.y;
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式（也使用统一步长）
            var baseX = -300; // 更新为新的基准点
            var baseY = -258;
            var stepXR = -43;
            var stepYR = 74;
            x = baseX + q * UNIFORM_STEP_X + r * stepXR;
            y = baseY - r * stepYR;
        }
        // 如果是单人头像预制体，往左上偏移一点点
        if (isPlayerAvatar) {
            y -= 12; // 往下偏移12像素（相比之前的-20，现在是-12，相当于往上调了8像素）
        }
        return cc.v2(x, y);
    };
    // 为六边形格子节点设置触摸事件
    HexChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C setupHexGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + q + "," + r + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onHexGridLongPress(q, r);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onHexGridClick(q, r, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 1 // 1 = 挖掘
        });
    };
    // 六边形格子长按事件 - 发送标记操作
    HexChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送标记操作事件 (action = 2)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 2 // 2 = 标记
        });
    };
    // 检查六边形坐标是否有效
    HexChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 在六边形格子上放置玩家预制体
    HexChessBoardController.prototype.placePlayerOnHexGrid = function (q, r, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C placePlayerOnHexGrid: \u65E0\u6548\u5750\u6807(" + q + "," + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 双重检查：确保格子为空
        if (!gridData || gridData.hasPlayer) {
            console.error("\u274C placePlayerOnHexGrid: \u683C\u5B50(" + q + "," + r + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置（单人头像预制体，y轴+20）
        var correctPosition = this.getHexWorldPosition(q, r, true);
        playerNode.setPosition(correctPosition);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, q, r, withFlag, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 安全地添加玩家节点（处理Layout限制）
    HexChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    HexChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, q, r, withFlag, onComplete) {
        var _a, _b;
        // 查找PlayerGameController组件（使用类引用）
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + q + "," + r + ")");
            }
            // 获取当前用户ID
            var currentUserId = ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "hex_player_" + q + "_" + r;
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = currentUserId;
            // 创建用户数据并设置头像
            var userData = {
                userId: currentUserId,
                nickName: "\u73A9\u5BB6(" + q + "," + r + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    // 获取默认头像URL
    HexChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    /**
     * 播放头像生成动画（由大变小，最终缩放为0.8）
     * @param playerNode 玩家节点
     */
    HexChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置最终缩放为0.8
        var finalScale = 0.8;
        // 设置初始缩放为1.2倍（比最终大一些，用于动画效果）
        var startScale = finalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画，最终缩放到0.8
        cc.tween(playerNode)
            .to(0.3, { scaleX: finalScale, scaleY: finalScale }, { easing: 'backOut' })
            .start();
    };
    // 清除指定六边形格子的玩家
    HexChessBoardController.prototype.clearHexGridPlayer = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    HexChessBoardController.prototype.clearAllPlayers = function () {
        var clearedCount = 0;
        // 1. 清理存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
                gridData.hasPlayer = false;
                clearedCount++;
            }
        });
        // 2. 清理棋盘上的其他玩家头像节点
        if (this.boardNode) {
            var children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                if (child.name === "player_game_pfb") {
                    // 检查是否有PlayerGameController组件
                    var playerController = child.getComponent(PlayerGameController_1.default);
                    if (playerController) {
                        child.removeFromParent();
                        clearedCount++;
                    }
                }
            }
        }
        console.log("\u6E05\u7406\u4E86 " + clearedCount + " \u4E2A\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9");
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     * 为了与四边形棋盘控制器保持一致的接口
     */
    HexChessBoardController.prototype.clearAllPlayerNodes = function () {
        this.clearAllPlayers();
    };
    // 获取所有已放置玩家的六边形坐标
    HexChessBoardController.prototype.getAllPlayerHexCoordinates = function () {
        var coordinates = [];
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                coordinates.push({ q: gridData.q, r: gridData.r });
            }
        });
        return coordinates;
    };
    // 检查六边形格子是否为空
    HexChessBoardController.prototype.isHexGridEmpty = function (q, r) {
        if (!this.isValidHexCoordinate(q, r)) {
            return false;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        return gridData ? !gridData.hasPlayer : false;
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    HexChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllHexGrids();
        // 重新初始化棋盘数据
        this.reinitializeHexBoardData();
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    HexChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除
     */
    HexChessBoardController.prototype.isGameElement = function (node, nodeName) {
        // 绝对不清除的节点（六边形小格子）
        if (nodeName.startsWith("HexGrid_") || nodeName === "hexblock") {
            return false;
        }
        // 分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        // UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_q_r 格式）
        if (nodeName.match(/^Test_-?\d+_-?\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent(PlayerGameController_1.default)) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        // 默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有六边形小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    HexChessBoardController.prototype.showAllHexGrids = function () {
        if (!this.boardNode) {
            return;
        }
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是六边形小格子节点
            if (child.name.startsWith("HexGrid_") || child.name === "hexblock") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的六边形小格子（点击时调用）
     */
    HexChessBoardController.prototype.hideHexGridAt = function (q, r) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 重新初始化六边形棋盘数据
     */
    HexChessBoardController.prototype.reinitializeHexBoardData = function () {
        // 重置hexGridData中的玩家状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
    };
    /**
     * 获取六边形格子数据
     */
    HexChessBoardController.prototype.getHexGridData = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.get(key) || null;
    };
    /**
     * 批量放置玩家（用于从服务器同步数据）
     */
    HexChessBoardController.prototype.batchPlaceHexPlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidHexCoordinate(coord.q, coord.r) && _this.isHexGridEmpty(coord.q, coord.r)) {
                _this.placePlayerOnHexGrid(coord.q, coord.r);
            }
        });
    };
    /**
     * 测试点击功能（调试用）
     */
    HexChessBoardController.prototype.testHexClick = function (q, r) {
        this.onHexGridClick(q, r);
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerHexCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 根据前端节点数量计算推荐的炸弹数量
     */
    HexChessBoardController.prototype.getRecommendedMineCount = function () {
        var gridCount = this.getHexGridCount();
        if (gridCount === 0) {
            return 13; // 默认值
        }
        // 约15%的格子是炸弹
        var mineCount = Math.floor(gridCount * 0.15);
        return Math.max(mineCount, 5); // 至少5个炸弹
    };
    /**
     * 测试六边形预制体位置计算是否正确
     */
    HexChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        console.log("🧪 测试六边形预制体位置计算（使用格子中心坐标）:");
        // 测试更新后的基准点坐标
        var testPoints = [
            { q: 0, r: 0, expected: { x: -300, y: -258 }, desc: "r=0行基准点(0,0)" },
            { q: 1, r: -1, expected: { x: -258, y: -184 }, desc: "r=-1行基准点(1,-1)" },
            { q: 1, r: -2, expected: { x: -300, y: -108 }, desc: "r=-2行基准点(1,-2)" },
            { q: 2, r: -3, expected: { x: -258, y: -36 }, desc: "r=-3行基准点(2,-3)" },
            { q: 2, r: -4, expected: { x: -300, y: 37 }, desc: "r=-4行基准点(2,-4)" },
            { q: 3, r: -5, expected: { x: -258, y: 110 }, desc: "r=-5行基准点(3,-5)" },
            { q: 3, r: -6, expected: { x: -300, y: 185 }, desc: "r=-6行基准点(3,-6)" },
            { q: 4, r: -7, expected: { x: -258, y: 260 }, desc: "r=-7行基准点(4,-7)" }
        ];
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 2 && errorY < 2; // 允许2像素误差
            if (isCorrect)
                correctCount++;
            console.log("  " + point.desc + "(" + point.q + "," + point.r + "): \u8BA1\u7B97(" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ") vs \u671F\u671B(" + point.expected.x + ", " + point.expected.y + ") \u8BEF\u5DEE(" + errorX.toFixed(1) + ", " + errorY.toFixed(1) + ") " + (isCorrect ? '✅' : '❌'));
        });
        console.log("\uD83D\uDCCA \u9884\u5236\u4F53\u4F4D\u7F6E\u6D4B\u8BD5\u7ED3\u679C: " + correctCount + "/" + testPoints.length + " \u4E2A\u5750\u6807\u70B9\u6B63\u786E");
        // 测试一些中间坐标
        console.log("\n🔍 测试中间坐标的计算结果:");
        var intermediatePoints = [
            // r=0行测试
            { q: 2, r: 0 }, { q: 3, r: 0 }, { q: 4, r: 0 }, { q: 5, r: 0 }, { q: 6, r: 0 },
            // r=-1行测试
            { q: 3, r: -1 }, { q: 4, r: -1 }, { q: 5, r: -1 }, { q: 6, r: -1 },
            // r=-2行测试
            { q: 3, r: -2 }, { q: 4, r: -2 }, { q: 5, r: -2 }, { q: 6, r: -2 }, { q: 7, r: -2 },
            // r=-3行测试
            { q: 4, r: -3 }, { q: 5, r: -3 }, { q: 6, r: -3 }, { q: 7, r: -3 }
        ];
        intermediatePoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            console.log("  (" + point.q + "," + point.r + "): (" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ")");
        });
        // 暴露到全局以便调试
        window.testHexPositions = function () { return _this.testHexPositionCalculation(); };
    };
    // ==================== NoticeActionDisplay 相关方法 ====================
    // 以下方法与第一张地图（方形地图）的逻辑完全一样，用于处理加分和掀开地图
    /**
     * 在指定六边形位置创建boom预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param isCurrentUser 是否是当前用户点到的雷
     */
    HexChessBoardController.prototype.createHexBoomPrefab = function (q, r, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
    };
    /**
     * 在指定六边形位置创建biaoji预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     */
    HexChessBoardController.prototype.createHexBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定六边形位置的neighborMines显示
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param neighborMines 周围地雷数量
     */
    HexChessBoardController.prototype.updateHexNeighborMinesDisplay = function (q, r, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createHexNumberPrefab(q, r, neighborMines);
    };
    /**
     * 创建六边形数字预制体（boom1, boom2, ...）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param number 数字
     */
    HexChessBoardController.prototype.createHexNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放棋盘震动动画（当前用户点到雷时）
     */
    HexChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            return;
        }
        var originalPosition = this.boardNode.getPosition();
        var shakeDistance = 10;
        cc.tween(this.boardNode)
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x })
            .start();
    };
    // ==================== 头像生命周期管理 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于管理头像预制体的生命周期
    /**
     * 让所有六边形头像消失（和第一张地图的hideAvatarsAtPosition逻辑一样）
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.hideAllHexAvatars = function (onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理六边形头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考第一张地图的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                avatarNodes.push(gridData.playerNode);
            }
        });
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        console.log("\u51C6\u5907\u8BA9 " + avatarNodes.length + " \u4E2A\u516D\u8FB9\u5F62\u5934\u50CF\u6D88\u5931");
        // 如果没有头像，直接执行回调
        if (avatarNodes.length === 0) {
            this.clearAllMyHexAvatarReferences();
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画（和第一张地图完全一样）
        avatarNodes.forEach(function (avatarNode) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用
                    _this.clearAllMyHexAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己六边形头像的引用（和第一张地图的clearAllMyAvatarReferences逻辑一样）
     */
    HexChessBoardController.prototype.clearAllMyHexAvatarReferences = function () {
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
        console.log("已清除所有六边形头像引用");
    };
    // ==================== 加分逻辑相关方法 ====================
    // 以下方法与第一张地图的加分逻辑完全一样
    /**
     * 在指定六边形位置的玩家节点上显示分数
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerNode = function (q, r, score, showPlusOne) {
        var _this = this;
        // 查找该位置的玩家节点
        var playerNode = this.findHexPlayerNodeAtPosition(q, r);
        if (!playerNode) {
            // 在NoticeActionDisplay流程中，头像会被清理，找不到节点是正常的
            console.log("\u5728\u516D\u8FB9\u5F62\u4F4D\u7F6E(" + q + ", " + r + ")\u627E\u4E0D\u5230\u73A9\u5BB6\u8282\u70B9\uFF0C\u8DF3\u8FC7\u5934\u50CF\u5206\u6570\u663E\u793A\uFF08\u6B63\u5E38\u73B0\u8C61\uFF09");
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnHexNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, score, null);
        }
    };
    /**
     * 查找指定六边形位置的玩家节点
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @returns 玩家节点或null
     */
    HexChessBoardController.prototype.findHexPlayerNodeAtPosition = function (q, r) {
        // 方法1: 从hexGridData中查找（自己的头像）
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode) {
            return gridData.playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 遍历棋盘上的所有子节点，查找player_game_pfb
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            if (child.name === "player_game_pfb") {
                // 检查位置是否匹配（允许一定的误差）
                var expectedPos = this.getHexWorldPosition(q, r, true);
                var actualPos = child.getPosition();
                var distance = expectedPos.sub(actualPos).mag();
                if (distance < 10) { // 10像素误差范围内
                    return child;
                }
            }
        }
        return null;
    };
    /**
     * 在六边形节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.showScoreAnimationOnHexNode = function (playerController, score, onComplete) {
        // 调用PlayerGameController的showAddScore方法
        if (playerController && typeof playerController.showAddScore === 'function') {
            playerController.showAddScore(score);
        }
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 显示玩家游戏加减分效果（完全复制四边形棋盘控制器的逻辑）
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    HexChessBoardController.prototype.showHexPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentHexUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在hexGridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentHexUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherHexUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID（复制四边形棋盘控制器的方法）
     */
    HexChessBoardController.prototype.getCurrentHexUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForCurrentHexUser = function (score) {
        var _this = this;
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                    gridData.playerNode.getComponent("PlayerGameController ");
                if (playerController) {
                    _this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                }
            }
        });
        return false;
    };
    /**
     * 为其他用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForOtherHexUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        return this.findHexPlayerNodeByUserId(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.findHexPlayerNodeByUserId = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerController = function (playerController, score) {
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
    };
    /**
     * 查找指定用户ID的所有六边形头像节点
     * @param userId 用户ID
     * @returns 头像节点数组
     */
    HexChessBoardController.prototype.findAllHexPlayerNodesByUserId = function (userId) {
        var playerNodes = [];
        if (!this.boardNode) {
            return playerNodes;
        }
        // 遍历棋盘上的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 检查是否是指定用户的头像（使用存储在节点上的userId）
                var storedUserId = child['userId'];
                if (storedUserId === userId) {
                    playerNodes.push(child);
                }
            }
        }
        // 也检查存储在hexGridData中的玩家节点
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent(PlayerGameController_1.default);
                var storedUserId = gridData.playerNode['userId'];
                if (playerController && storedUserId === userId) {
                    // 避免重复添加
                    if (!playerNodes.includes(gridData.playerNode)) {
                        playerNodes.push(gridData.playerNode);
                    }
                }
            }
        });
        return playerNodes;
    };
    // ==================== 其他玩家头像生成 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于生成其他玩家的头像
    /**
     * 在指定六边形位置显示其他玩家的操作（和第一张地图的displayOtherPlayersAtPosition逻辑一样）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 该位置的其他玩家操作列表
     */
    HexChessBoardController.prototype.displayOtherPlayersAtHexPosition = function (q, r, actions) {
        if (!this.isValidHexCoordinate(q, r) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + q + ", " + r + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingHexGrid(q, r, actions);
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyHexGrid(q, r, actions);
        }
    };
    /**
     * 在已有自己头像的六边形格子上添加其他玩家头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToExistingHexGrid = function (q, r, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getHexPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyHexAvatarPosition(q, r, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 在空六边形格子上添加其他玩家头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToEmptyHexGrid = function (q, r, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getHexPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 获取六边形多人布局的位置信息（和第一张地图的getPlayerPositions逻辑一样）
     * @param playerCount 玩家数量
     * @returns 位置数组
     */
    HexChessBoardController.prototype.getHexPlayerPositions = function (playerCount) {
        // 复用方形地图的多人布局逻辑
        var positions = [
            // 1人：中心位置
            [{ x: 0, y: 0, scale: 0.8 }],
            // 2人：左右分布
            [{ x: -15, y: 0, scale: 0.7 }, { x: 15, y: 0, scale: 0.7 }],
            // 3人：三角形分布
            [{ x: 0, y: 10, scale: 0.6 }, { x: -12, y: -8, scale: 0.6 }, { x: 12, y: -8, scale: 0.6 }],
            // 4人：四角分布
            [{ x: -10, y: 8, scale: 0.5 }, { x: 10, y: 8, scale: 0.5 }, { x: -10, y: -8, scale: 0.5 }, { x: 10, y: -8, scale: 0.5 }]
        ];
        return positions[Math.min(playerCount, 4) - 1] || positions[3];
    };
    /**
     * 调整自己的六边形头像位置和缩放（当多人在同一格子时）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.adjustMyHexAvatarPosition = function (q, r, position, actions) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 查找自己的头像节点
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            console.warn("\u5728\u516D\u8FB9\u5F62\u4F4D\u7F6E(" + q + ", " + r + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = gridData.playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 计算基础位置
        var basePosition = this.getHexWorldPosition(q, r, true);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩小动画
        this.playHexAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 创建其他玩家在六边形位置的头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param action 玩家操作数据
     * @param position 位置和缩放信息
     * @param totalPlayers 总玩家数
     */
    HexChessBoardController.prototype.createOtherPlayerAtHexPosition = function (q, r, action, position, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab || !this.boardNode) {
            console.error("❌ 预制体或棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算基础位置
        var basePosition = this.getHexWorldPosition(q, r, true);
        // 计算最终位置
        var finalPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        playerNode.setPosition(finalPosition);
        playerNode.setScale(position.scale);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 添加到棋盘
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerHexAvatar(playerNode, action, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
    };
    /**
     * 设置其他玩家的六边形头像和数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.setupOtherPlayerHexAvatar = function (playerNode, action, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = action.userId;
            // 设置旗子节点的显示状态
            var withFlag_1 = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag_1;
            }
            // 获取真实的用户数据（和第一张地图逻辑一样）
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u6570\u636E");
                // 使用默认数据作为备选
                realUserData = {
                    userId: action.userId,
                    nickName: "\u73A9\u5BB6" + action.userId,
                    avatar: this.getDefaultAvatarUrl(),
                    score: 0,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    rank: 0
                };
            }
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(realUserData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag_1;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置其他玩家头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    /**
     * 获取其他玩家的头像URL
     * @param userId 用户ID
     * @returns 头像URL
     */
    HexChessBoardController.prototype.getOtherPlayerAvatarUrl = function (userId) {
        // 这里可以根据userId获取真实的头像URL
        // 暂时使用默认头像
        return this.getDefaultAvatarUrl();
    };
    /**
     * 播放六边形头像调整动画
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    HexChessBoardController.prototype.playHexAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        // 播放平滑移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 从GlobalBean中获取真实的用户数据（和第一张地图逻辑完全一样）
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    HexChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexChessBoardController.prototype, "boardNode", void 0);
    HexChessBoardController = __decorate([
        ccclass
    ], HexChessBoardController);
    return HexChessBoardController;
}(cc.Component));
exports.default = HexChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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