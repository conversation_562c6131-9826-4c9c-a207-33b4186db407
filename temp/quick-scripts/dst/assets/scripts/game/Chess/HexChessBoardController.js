
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd9f2eirVKNNorhz2LDmkG2T', 'HexChessBoardController');
// scripts/game/Chess/HexChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexChessBoardController = /** @class */ (function (_super) {
    __extends(HexChessBoardController, _super);
    function HexChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null;
        _this.boardNode = null; // 棋盘节点
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        return _this;
    }
    HexChessBoardController.prototype.onLoad = function () {
    };
    HexChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.boardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var coords = this_1.parseHexCoordinateFromName(child.name);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(child.name);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                }
            }
        }
    };
    // 从节点名称解析六边形坐标
    HexChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 从位置计算六边形坐标（近似）
    HexChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 六边形坐标转换（从像素坐标到六边形坐标）
        var x = pos.x;
        var y = pos.y;
        // 使用六边形坐标转换公式
        var q = Math.round((Math.sqrt(3) / 3 * x - 1 / 3 * y) / this.HEX_SIZE);
        var r = Math.round((2 / 3 * y) / this.HEX_SIZE);
        // 检查是否为有效坐标
        if (this.isValidHexCoordinate(q, r)) {
            return { q: q, r: r };
        }
        return null;
    };
    // 计算六边形预制体的生成位置（直接使用您提供的格子中心坐标）
    HexChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        // 您提供的精确格子中心坐标
        var exactCoords = new Map();
        // 更新后的基准点坐标（与rowData保持一致）
        exactCoords.set("0,0", cc.v2(-300, -258)); // r=0行基准点
        exactCoords.set("1,-1", cc.v2(-258, -184)); // r=-1行基准点
        exactCoords.set("1,-2", cc.v2(-300, -108)); // r=-2行基准点
        exactCoords.set("2,-3", cc.v2(-258, -36)); // r=-3行基准点
        exactCoords.set("2,-4", cc.v2(-300, 37)); // r=-4行基准点
        exactCoords.set("3,-5", cc.v2(-258, 110)); // r=-5行基准点
        exactCoords.set("3,-6", cc.v2(-300, 185)); // r=-6行基准点
        exactCoords.set("4,-7", cc.v2(-258, 260)); // r=-7行基准点
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (exactCoords.has(key)) {
            var pos = exactCoords.get(key);
            // 如果是单人头像预制体，往左上偏移一点点
            if (isPlayerAvatar) {
                return cc.v2(pos.x + 5, pos.y - 12);
            }
            return pos;
        }
        // 对于其他坐标，使用基于您提供的精确坐标数据进行计算
        // 定义每一行的数据：使用统一步长86，保证美观整齐
        var UNIFORM_STEP_X = 86; // 统一的x方向步长
        var rowData = new Map();
        // 基于您提供的更新数据，使用统一步长86
        rowData.set(0, { baseQ: 0, baseX: -300, y: -258 }); // r=0行：基准点(0,0) → (-300, -258)
        rowData.set(-1, { baseQ: 1, baseX: -258, y: -184 }); // r=-1行：基准点(1,-1) → (-258, -184)
        rowData.set(-2, { baseQ: 1, baseX: -300, y: -108 }); // r=-2行：基准点(1,-2) → (-300, -108)
        rowData.set(-3, { baseQ: 2, baseX: -258, y: -36 }); // r=-3行：基准点(2,-3) → (-258, -36)
        rowData.set(-4, { baseQ: 2, baseX: -300, y: 37 }); // r=-4行：基准点(2,-4) → (-300, 37)
        rowData.set(-5, { baseQ: 3, baseX: -258, y: 110 }); // r=-5行：基准点(3,-5) → (-258, 110)
        rowData.set(-6, { baseQ: 3, baseX: -300, y: 185 }); // r=-6行：基准点(3,-6) → (-300, 185)
        rowData.set(-7, { baseQ: 4, baseX: -258, y: 260 }); // r=-7行：基准点(4,-7) → (-258, 260)
        // 计算基础位置
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (rowData.has(r)) {
            var data = rowData.get(r);
            x = data.baseX + (q - data.baseQ) * UNIFORM_STEP_X;
            y = data.y;
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式（也使用统一步长）
            var baseX = -300; // 更新为新的基准点
            var baseY = -258;
            var stepXR = -43;
            var stepYR = 74;
            x = baseX + q * UNIFORM_STEP_X + r * stepXR;
            y = baseY - r * stepYR;
        }
        // 如果是单人头像预制体，往左上偏移一点点
        if (isPlayerAvatar) {
            y -= 12; // 往下偏移12像素（相比之前的-20，现在是-12，相当于往上调了8像素）
        }
        return cc.v2(x, y);
    };
    // 为六边形格子节点设置触摸事件
    HexChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C setupHexGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + q + "," + r + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onHexGridLongPress(q, r);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onHexGridClick(q, r, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 1 // 1 = 挖掘
        });
    };
    // 六边形格子长按事件 - 发送标记操作
    HexChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送标记操作事件 (action = 2)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 2 // 2 = 标记
        });
    };
    // 检查六边形坐标是否有效
    HexChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 在六边形格子上放置玩家预制体
    HexChessBoardController.prototype.placePlayerOnHexGrid = function (q, r, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C placePlayerOnHexGrid: \u65E0\u6548\u5750\u6807(" + q + "," + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 双重检查：确保格子为空
        if (!gridData || gridData.hasPlayer) {
            console.error("\u274C placePlayerOnHexGrid: \u683C\u5B50(" + q + "," + r + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置（单人头像预制体，y轴+20）
        var correctPosition = this.getHexWorldPosition(q, r, true);
        playerNode.setPosition(correctPosition);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, q, r, withFlag, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 安全地添加玩家节点（处理Layout限制）
    HexChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    HexChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, q, r, withFlag, onComplete) {
        var _a, _b;
        // 查找PlayerGameController组件（使用类引用）
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + q + "," + r + ")");
            }
            // 获取当前用户ID
            var currentUserId = ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "hex_player_" + q + "_" + r;
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = currentUserId;
            // 创建用户数据并设置头像
            var userData = {
                userId: currentUserId,
                nickName: "\u73A9\u5BB6(" + q + "," + r + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    // 获取默认头像URL
    HexChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    /**
     * 播放头像生成动画（由大变小，最终缩放为0.8）
     * @param playerNode 玩家节点
     */
    HexChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置最终缩放为0.8
        var finalScale = 0.8;
        // 设置初始缩放为1.2倍（比最终大一些，用于动画效果）
        var startScale = finalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画，最终缩放到0.8
        cc.tween(playerNode)
            .to(0.3, { scaleX: finalScale, scaleY: finalScale }, { easing: 'backOut' })
            .start();
    };
    // 清除指定六边形格子的玩家
    HexChessBoardController.prototype.clearHexGridPlayer = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    HexChessBoardController.prototype.clearAllPlayers = function () {
        var clearedCount = 0;
        // 1. 清理存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
                gridData.hasPlayer = false;
                clearedCount++;
            }
        });
        // 2. 清理棋盘上的其他玩家头像节点
        if (this.boardNode) {
            var children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                if (child.name === "player_game_pfb") {
                    // 检查是否有PlayerGameController组件
                    var playerController = child.getComponent(PlayerGameController_1.default);
                    if (playerController) {
                        child.removeFromParent();
                        clearedCount++;
                    }
                }
            }
        }
        console.log("\u6E05\u7406\u4E86 " + clearedCount + " \u4E2A\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9");
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     * 为了与四边形棋盘控制器保持一致的接口
     */
    HexChessBoardController.prototype.clearAllPlayerNodes = function () {
        this.clearAllPlayers();
    };
    // 获取所有已放置玩家的六边形坐标
    HexChessBoardController.prototype.getAllPlayerHexCoordinates = function () {
        var coordinates = [];
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                coordinates.push({ q: gridData.q, r: gridData.r });
            }
        });
        return coordinates;
    };
    // 检查六边形格子是否为空
    HexChessBoardController.prototype.isHexGridEmpty = function (q, r) {
        if (!this.isValidHexCoordinate(q, r)) {
            return false;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        return gridData ? !gridData.hasPlayer : false;
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    HexChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllHexGrids();
        // 重新初始化棋盘数据
        this.reinitializeHexBoardData();
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    HexChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除
     */
    HexChessBoardController.prototype.isGameElement = function (node, nodeName) {
        // 绝对不清除的节点（六边形小格子）
        if (nodeName.startsWith("HexGrid_") || nodeName === "hexblock") {
            return false;
        }
        // 分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        // UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_q_r 格式）
        if (nodeName.match(/^Test_-?\d+_-?\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent(PlayerGameController_1.default)) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        // 默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有六边形小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    HexChessBoardController.prototype.showAllHexGrids = function () {
        if (!this.boardNode) {
            return;
        }
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是六边形小格子节点
            if (child.name.startsWith("HexGrid_") || child.name === "hexblock") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的六边形小格子（点击时调用）
     */
    HexChessBoardController.prototype.hideHexGridAt = function (q, r) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 重新初始化六边形棋盘数据
     */
    HexChessBoardController.prototype.reinitializeHexBoardData = function () {
        // 重置hexGridData中的玩家状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
    };
    /**
     * 获取六边形格子数据
     */
    HexChessBoardController.prototype.getHexGridData = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.get(key) || null;
    };
    /**
     * 批量放置玩家（用于从服务器同步数据）
     */
    HexChessBoardController.prototype.batchPlaceHexPlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidHexCoordinate(coord.q, coord.r) && _this.isHexGridEmpty(coord.q, coord.r)) {
                _this.placePlayerOnHexGrid(coord.q, coord.r);
            }
        });
    };
    /**
     * 测试点击功能（调试用）
     */
    HexChessBoardController.prototype.testHexClick = function (q, r) {
        this.onHexGridClick(q, r);
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerHexCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 根据前端节点数量计算推荐的炸弹数量
     */
    HexChessBoardController.prototype.getRecommendedMineCount = function () {
        var gridCount = this.getHexGridCount();
        if (gridCount === 0) {
            return 13; // 默认值
        }
        // 约15%的格子是炸弹
        var mineCount = Math.floor(gridCount * 0.15);
        return Math.max(mineCount, 5); // 至少5个炸弹
    };
    /**
     * 测试六边形预制体位置计算是否正确
     */
    HexChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        console.log("🧪 测试六边形预制体位置计算（使用格子中心坐标）:");
        // 测试更新后的基准点坐标
        var testPoints = [
            { q: 0, r: 0, expected: { x: -300, y: -258 }, desc: "r=0行基准点(0,0)" },
            { q: 1, r: -1, expected: { x: -258, y: -184 }, desc: "r=-1行基准点(1,-1)" },
            { q: 1, r: -2, expected: { x: -300, y: -108 }, desc: "r=-2行基准点(1,-2)" },
            { q: 2, r: -3, expected: { x: -258, y: -36 }, desc: "r=-3行基准点(2,-3)" },
            { q: 2, r: -4, expected: { x: -300, y: 37 }, desc: "r=-4行基准点(2,-4)" },
            { q: 3, r: -5, expected: { x: -258, y: 110 }, desc: "r=-5行基准点(3,-5)" },
            { q: 3, r: -6, expected: { x: -300, y: 185 }, desc: "r=-6行基准点(3,-6)" },
            { q: 4, r: -7, expected: { x: -258, y: 260 }, desc: "r=-7行基准点(4,-7)" }
        ];
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 2 && errorY < 2; // 允许2像素误差
            if (isCorrect)
                correctCount++;
            console.log("  " + point.desc + "(" + point.q + "," + point.r + "): \u8BA1\u7B97(" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ") vs \u671F\u671B(" + point.expected.x + ", " + point.expected.y + ") \u8BEF\u5DEE(" + errorX.toFixed(1) + ", " + errorY.toFixed(1) + ") " + (isCorrect ? '✅' : '❌'));
        });
        console.log("\uD83D\uDCCA \u9884\u5236\u4F53\u4F4D\u7F6E\u6D4B\u8BD5\u7ED3\u679C: " + correctCount + "/" + testPoints.length + " \u4E2A\u5750\u6807\u70B9\u6B63\u786E");
        // 测试一些中间坐标
        console.log("\n🔍 测试中间坐标的计算结果:");
        var intermediatePoints = [
            // r=0行测试
            { q: 2, r: 0 }, { q: 3, r: 0 }, { q: 4, r: 0 }, { q: 5, r: 0 }, { q: 6, r: 0 },
            // r=-1行测试
            { q: 3, r: -1 }, { q: 4, r: -1 }, { q: 5, r: -1 }, { q: 6, r: -1 },
            // r=-2行测试
            { q: 3, r: -2 }, { q: 4, r: -2 }, { q: 5, r: -2 }, { q: 6, r: -2 }, { q: 7, r: -2 },
            // r=-3行测试
            { q: 4, r: -3 }, { q: 5, r: -3 }, { q: 6, r: -3 }, { q: 7, r: -3 }
        ];
        intermediatePoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            console.log("  (" + point.q + "," + point.r + "): (" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ")");
        });
        // 暴露到全局以便调试
        window.testHexPositions = function () { return _this.testHexPositionCalculation(); };
    };
    // ==================== NoticeActionDisplay 相关方法 ====================
    // 以下方法与第一张地图（方形地图）的逻辑完全一样，用于处理加分和掀开地图
    /**
     * 在指定六边形位置创建boom预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param isCurrentUser 是否是当前用户点到的雷
     */
    HexChessBoardController.prototype.createHexBoomPrefab = function (q, r, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
    };
    /**
     * 在指定六边形位置创建biaoji预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     */
    HexChessBoardController.prototype.createHexBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定六边形位置的neighborMines显示
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param neighborMines 周围地雷数量
     */
    HexChessBoardController.prototype.updateHexNeighborMinesDisplay = function (q, r, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createHexNumberPrefab(q, r, neighborMines);
    };
    /**
     * 创建六边形数字预制体（boom1, boom2, ...）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param number 数字
     */
    HexChessBoardController.prototype.createHexNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放棋盘震动动画（当前用户点到雷时）
     */
    HexChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            return;
        }
        var originalPosition = this.boardNode.getPosition();
        var shakeDistance = 10;
        cc.tween(this.boardNode)
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x })
            .start();
    };
    // ==================== 头像生命周期管理 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于管理头像预制体的生命周期
    /**
     * 让所有六边形头像消失（和第一张地图的hideAvatarsAtPosition逻辑一样）
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.hideAllHexAvatars = function (onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理六边形头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考第一张地图的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                avatarNodes.push(gridData.playerNode);
            }
        });
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        console.log("\u51C6\u5907\u8BA9 " + avatarNodes.length + " \u4E2A\u516D\u8FB9\u5F62\u5934\u50CF\u6D88\u5931");
        // 如果没有头像，直接执行回调
        if (avatarNodes.length === 0) {
            this.clearAllMyHexAvatarReferences();
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画（和第一张地图完全一样）
        avatarNodes.forEach(function (avatarNode) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用
                    _this.clearAllMyHexAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己六边形头像的引用（和第一张地图的clearAllMyAvatarReferences逻辑一样）
     */
    HexChessBoardController.prototype.clearAllMyHexAvatarReferences = function () {
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
        console.log("已清除所有六边形头像引用");
    };
    // ==================== 加分逻辑相关方法 ====================
    // 以下方法与第一张地图的加分逻辑完全一样
    /**
     * 在指定六边形位置的玩家节点上显示分数
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerNode = function (q, r, score, showPlusOne) {
        var _this = this;
        // 查找该位置的玩家节点
        var playerNode = this.findHexPlayerNodeAtPosition(q, r);
        if (!playerNode) {
            // 在NoticeActionDisplay流程中，头像会被清理，找不到节点是正常的
            console.log("\u5728\u516D\u8FB9\u5F62\u4F4D\u7F6E(" + q + ", " + r + ")\u627E\u4E0D\u5230\u73A9\u5BB6\u8282\u70B9\uFF0C\u8DF3\u8FC7\u5934\u50CF\u5206\u6570\u663E\u793A\uFF08\u6B63\u5E38\u73B0\u8C61\uFF09");
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnHexNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, score, null);
        }
    };
    /**
     * 查找指定六边形位置的玩家节点
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @returns 玩家节点或null
     */
    HexChessBoardController.prototype.findHexPlayerNodeAtPosition = function (q, r) {
        // 方法1: 从hexGridData中查找（自己的头像）
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode) {
            return gridData.playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 遍历棋盘上的所有子节点，查找player_game_pfb
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            if (child.name === "player_game_pfb") {
                // 检查位置是否匹配（允许一定的误差）
                var expectedPos = this.getHexWorldPosition(q, r, true);
                var actualPos = child.getPosition();
                var distance = expectedPos.sub(actualPos).mag();
                if (distance < 10) { // 10像素误差范围内
                    return child;
                }
            }
        }
        return null;
    };
    /**
     * 在六边形节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.showScoreAnimationOnHexNode = function (playerController, score, onComplete) {
        // 调用PlayerGameController的showAddScore方法
        if (playerController && typeof playerController.showAddScore === 'function') {
            playerController.showAddScore(score);
        }
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 显示玩家游戏加减分效果（完全复制四边形棋盘控制器的逻辑）
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    HexChessBoardController.prototype.showHexPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentHexUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在hexGridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentHexUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherHexUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID（复制四边形棋盘控制器的方法）
     */
    HexChessBoardController.prototype.getCurrentHexUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForCurrentHexUser = function (score) {
        var _this = this;
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                    gridData.playerNode.getComponent("PlayerGameController ");
                if (playerController) {
                    _this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                }
            }
        });
        return false;
    };
    /**
     * 为其他用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForOtherHexUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        return this.findHexPlayerNodeByUserId(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.findHexPlayerNodeByUserId = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.0); // 2秒后恢复，根据分数动画时长调整
    };
    /**
     * 查找指定用户ID的所有六边形头像节点
     * @param userId 用户ID
     * @returns 头像节点数组
     */
    HexChessBoardController.prototype.findAllHexPlayerNodesByUserId = function (userId) {
        var playerNodes = [];
        if (!this.boardNode) {
            return playerNodes;
        }
        // 遍历棋盘上的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 检查是否是指定用户的头像（使用存储在节点上的userId）
                var storedUserId = child['userId'];
                if (storedUserId === userId) {
                    playerNodes.push(child);
                }
            }
        }
        // 也检查存储在hexGridData中的玩家节点
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent(PlayerGameController_1.default);
                var storedUserId = gridData.playerNode['userId'];
                if (playerController && storedUserId === userId) {
                    // 避免重复添加
                    if (!playerNodes.includes(gridData.playerNode)) {
                        playerNodes.push(gridData.playerNode);
                    }
                }
            }
        });
        return playerNodes;
    };
    // ==================== 其他玩家头像生成 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于生成其他玩家的头像
    /**
     * 在指定六边形位置显示其他玩家的操作（和第一张地图的displayOtherPlayersAtPosition逻辑一样）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 该位置的其他玩家操作列表
     */
    HexChessBoardController.prototype.displayOtherPlayersAtHexPosition = function (q, r, actions) {
        if (!this.isValidHexCoordinate(q, r) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + q + ", " + r + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingHexGrid(q, r, actions);
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyHexGrid(q, r, actions);
        }
    };
    /**
     * 在已有自己头像的六边形格子上添加其他玩家头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToExistingHexGrid = function (q, r, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getHexPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyHexAvatarPosition(q, r, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 在空六边形格子上添加其他玩家头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToEmptyHexGrid = function (q, r, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getHexPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 获取六边形多人布局的位置信息（和第一张地图的getPlayerPositions逻辑一样）
     * @param playerCount 玩家数量
     * @returns 位置数组
     */
    HexChessBoardController.prototype.getHexPlayerPositions = function (playerCount) {
        // 复用方形地图的多人布局逻辑
        var positions = [
            // 1人：中心位置
            [{ x: 0, y: 0, scale: 0.8 }],
            // 2人：左右分布
            [{ x: -15, y: 0, scale: 0.7 }, { x: 15, y: 0, scale: 0.7 }],
            // 3人：三角形分布
            [{ x: 0, y: 10, scale: 0.6 }, { x: -12, y: -8, scale: 0.6 }, { x: 12, y: -8, scale: 0.6 }],
            // 4人：四角分布
            [{ x: -10, y: 8, scale: 0.5 }, { x: 10, y: 8, scale: 0.5 }, { x: -10, y: -8, scale: 0.5 }, { x: 10, y: -8, scale: 0.5 }]
        ];
        return positions[Math.min(playerCount, 4) - 1] || positions[3];
    };
    /**
     * 调整自己的六边形头像位置和缩放（当多人在同一格子时）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.adjustMyHexAvatarPosition = function (q, r, position, actions) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 查找自己的头像节点
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            console.warn("\u5728\u516D\u8FB9\u5F62\u4F4D\u7F6E(" + q + ", " + r + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = gridData.playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 计算基础位置
        var basePosition = this.getHexWorldPosition(q, r, true);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩小动画
        this.playHexAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 创建其他玩家在六边形位置的头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param action 玩家操作数据
     * @param position 位置和缩放信息
     * @param totalPlayers 总玩家数
     */
    HexChessBoardController.prototype.createOtherPlayerAtHexPosition = function (q, r, action, position, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab || !this.boardNode) {
            console.error("❌ 预制体或棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算基础位置
        var basePosition = this.getHexWorldPosition(q, r, true);
        // 计算最终位置
        var finalPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        playerNode.setPosition(finalPosition);
        playerNode.setScale(position.scale);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 添加到棋盘
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerHexAvatar(playerNode, action, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
    };
    /**
     * 设置其他玩家的六边形头像和数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.setupOtherPlayerHexAvatar = function (playerNode, action, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = action.userId;
            // 设置旗子节点的显示状态
            var withFlag_1 = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag_1;
            }
            // 获取真实的用户数据（和第一张地图逻辑一样）
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u6570\u636E");
                // 使用默认数据作为备选
                realUserData = {
                    userId: action.userId,
                    nickName: "\u73A9\u5BB6" + action.userId,
                    avatar: this.getDefaultAvatarUrl(),
                    score: 0,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    rank: 0
                };
            }
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(realUserData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag_1;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置其他玩家头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    /**
     * 获取其他玩家的头像URL
     * @param userId 用户ID
     * @returns 头像URL
     */
    HexChessBoardController.prototype.getOtherPlayerAvatarUrl = function (userId) {
        // 这里可以根据userId获取真实的头像URL
        // 暂时使用默认头像
        return this.getDefaultAvatarUrl();
    };
    /**
     * 播放六边形头像调整动画
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    HexChessBoardController.prototype.playHexAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        // 播放平滑移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 从GlobalBean中获取真实的用户数据（和第一张地图逻辑完全一样）
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    HexChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexChessBoardController.prototype, "boardNode", void 0);
    HexChessBoardController = __decorate([
        ccclass
    ], HexChessBoardController);
    return HexChessBoardController;
}(cc.Component));
exports.default = HexChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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