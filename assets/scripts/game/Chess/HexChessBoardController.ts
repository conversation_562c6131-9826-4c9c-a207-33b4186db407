// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { RoomUser, HexCoord, PlayerActionDisplay } from "../../bean/GameBean";
import { GlobalBean } from "../../bean/GlobalBean";
import PlayerGameController from "../../pfb/PlayerGameController ";

const {ccclass, property} = cc._decorator;

// 六边形格子数据接口
export interface HexGridData {
    q: number;  // 六边形坐标系q坐标
    r: number;  // 六边形坐标系r坐标
    worldPos: cc.Vec2;  // 格子在世界坐标系中的位置
    hasPlayer: boolean;  // 是否已经放置了玩家预制体
    playerNode?: cc.Node;  // 放置的玩家节点引用
}

@ccclass
export default class HexChessBoardController extends cc.Component {

    @property(cc.Prefab)
    playerGamePrefab: cc.Prefab = null;

    @property(cc.Prefab)
    boomPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    biaojiPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom1Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom2Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom3Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom4Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom5Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom6Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom7Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom8Prefab: cc.Prefab = null;

    @property(cc.Node)
    boardNode: cc.Node = null;  // 棋盘节点

    // 六边形棋盘配置
    private readonly HEX_SIZE = 44;  // 六边形半径
    private readonly HEX_WIDTH = this.HEX_SIZE * 2;  // 六边形宽度
    private readonly HEX_HEIGHT = this.HEX_SIZE * Math.sqrt(3);  // 六边形高度

    // 格子数据存储 - 使用Map存储六边形坐标
    private hexGridData: Map<string, HexGridData> = new Map();  // 存储六边形格子数据
    private hexGridNodes: Map<string, cc.Node> = new Map();  // 存储六边形格子节点
    private validHexCoords: HexCoord[] = [];  // 有效的六边形坐标列表

    onLoad() {
    
    }

    start() {
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(() => {

            this.setValidHexCoords([]);  // 传入空数组，但会被忽略

            // 测试预制体位置计算
            this.testHexPositionCalculation();

            this.enableTouchForExistingGrids();
        }, 0.1);
    }

    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    public setValidHexCoords(_coords: HexCoord[]) {


        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();

        this.initHexBoard();
    }

    /**
     * 从节点名称自动生成有效坐标列表
     */
    private generateCoordsFromNodeNames() {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }

        const foundCoords: HexCoord[] = [];
        const children = this.boardNode.children;

       

        for (let i = 0; i < children.length; i++) {
            const child = children[i];
            const coords = this.parseHexCoordinateFromName(child.name);

            if (coords) {
                // 检查是否已经存在相同的坐标
                const exists = foundCoords.some(c => c.q === coords.q && c.r === coords.r);
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                   
                }
            }
        }

        this.validHexCoords = foundCoords;
       
    }

    // 初始化六边形棋盘
    private initHexBoard() {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();

        // 初始化有效坐标的数据
        for (const coord of this.validHexCoords) {
            const key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }

        this.createHexGridNodes();
    }

    // 生成六边形坐标的唯一键
    private getHexKey(q: number, r: number): string {
        return `${q},${r}`;
    }

    // 启用现有格子的触摸事件
    private createHexGridNodes() {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }

        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    }

    // 为现有格子启用触摸事件
    private enableTouchForExistingGrids() {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }

        // 遍历棋盘节点的所有子节点
        let children = this.boardNode.children;

        for (let i = 0; i < children.length; i++) {
            let child = children[i];

            // 尝试从节点名称解析六边形坐标
            let coords = this.parseHexCoordinateFromName(child.name);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                const key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
            } else {
                // 如果无法从名称解析，尝试从位置计算
                let pos = child.getPosition();
                let coords = this.getHexCoordinateFromPosition(pos);
                if (coords) {
                    this.setupHexGridTouchEvents(child, coords.q, coords.r);
                    const key = this.getHexKey(coords.q, coords.r);
                    this.hexGridNodes.set(key, child);
                }
            }
        }
    }

    // 从节点名称解析六边形坐标
    private parseHexCoordinateFromName(nodeName: string): {q: number, r: number} | null {
       

        
        const patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,   // sixblock_q_r 格式（您使用的格式）
        ];

        for (const pattern of patterns) {
            const match = nodeName.match(pattern);
            if (match) {
                const coords = {q: parseInt(match[1]), r: parseInt(match[2])};
              
                return coords;
            }
        }

        console.warn(`❌ 无法解析节点名称: ${nodeName}`);
        return null;
    }

    // 从位置计算六边形坐标（近似）
    private getHexCoordinateFromPosition(pos: cc.Vec2): {q: number, r: number} | null {
        // 六边形坐标转换（从像素坐标到六边形坐标）
        const x = pos.x;
        const y = pos.y;
        
        // 使用六边形坐标转换公式
        const q = Math.round((Math.sqrt(3)/3 * x - 1/3 * y) / this.HEX_SIZE);
        const r = Math.round((2/3 * y) / this.HEX_SIZE);

        // 检查是否为有效坐标
        if (this.isValidHexCoordinate(q, r)) {
            return {q: q, r: r};
        }
        return null;
    }

    // 计算六边形预制体的生成位置（直接使用您提供的格子中心坐标）
    private getHexWorldPosition(q: number, r: number, isPlayerAvatar: boolean = false): cc.Vec2 {
        // 您提供的精确格子中心坐标
        const exactCoords = new Map<string, cc.Vec2>();
        // 更新后的基准点坐标（与rowData保持一致）
        exactCoords.set("0,0", cc.v2(-300, -258));   // r=0行基准点
        exactCoords.set("1,-1", cc.v2(-258, -184));  // r=-1行基准点
        exactCoords.set("1,-2", cc.v2(-300, -108));  // r=-2行基准点
        exactCoords.set("2,-3", cc.v2(-258, -36));   // r=-3行基准点
        exactCoords.set("2,-4", cc.v2(-300, 37));    // r=-4行基准点
        exactCoords.set("3,-5", cc.v2(-258, 110));   // r=-5行基准点
        exactCoords.set("3,-6", cc.v2(-300, 185));   // r=-6行基准点
        exactCoords.set("4,-7", cc.v2(-258, 260));   // r=-7行基准点

        // 首先检查是否有精确坐标
        const key = `${q},${r}`;
        if (exactCoords.has(key)) {
            const pos = exactCoords.get(key);
            // 如果是单人头像预制体，往左上偏移一点点
            if (isPlayerAvatar) {
                return cc.v2(pos.x+5 , pos.y - 12);
            }
            return pos;
        }

        // 对于其他坐标，使用基于您提供的精确坐标数据进行计算

        // 定义每一行的数据：使用统一步长86，保证美观整齐
        const UNIFORM_STEP_X = 86; // 统一的x方向步长
        const rowData = new Map<number, {baseQ: number, baseX: number, y: number}>();

        // 基于您提供的更新数据，使用统一步长86
        rowData.set(0, { baseQ: 0, baseX: -300, y: -258 });   // r=0行：基准点(0,0) → (-300, -258)
        rowData.set(-1, { baseQ: 1, baseX: -258, y: -184 });  // r=-1行：基准点(1,-1) → (-258, -184)
        rowData.set(-2, { baseQ: 1, baseX: -300, y: -108 });  // r=-2行：基准点(1,-2) → (-300, -108)
        rowData.set(-3, { baseQ: 2, baseX: -258, y: -36 });   // r=-3行：基准点(2,-3) → (-258, -36)
        rowData.set(-4, { baseQ: 2, baseX: -300, y: 37 });    // r=-4行：基准点(2,-4) → (-300, 37)
        rowData.set(-5, { baseQ: 3, baseX: -258, y: 110 });   // r=-5行：基准点(3,-5) → (-258, 110)
        rowData.set(-6, { baseQ: 3, baseX: -300, y: 185 });   // r=-6行：基准点(3,-6) → (-300, 185)
        rowData.set(-7, { baseQ: 4, baseX: -258, y: 260 });   // r=-7行：基准点(4,-7) → (-258, 260)

        // 计算基础位置
        let x: number, y: number;

        // 如果有该行的数据，使用统一步长计算
        if (rowData.has(r)) {
            const data = rowData.get(r);
            x = data.baseX + (q - data.baseQ) * UNIFORM_STEP_X;
            y = data.y;
        } else {
            // 对于其他行，使用通用的六边形轴线坐标系公式（也使用统一步长）
            const baseX = -300;  // 更新为新的基准点
            const baseY = -258;
            const stepXR = -43;
            const stepYR = 74;

            x = baseX + q * UNIFORM_STEP_X + r * stepXR;
            y = baseY - r * stepYR;
        }

        // 如果是单人头像预制体，往左上偏移一点点
        if (isPlayerAvatar) {
            y -= 12; // 往下偏移12像素（相比之前的-20，现在是-12，相当于往上调了8像素）
        }

        return cc.v2(x, y);
    }

    // 为六边形格子节点设置触摸事件
    private setupHexGridTouchEvents(gridNode: cc.Node, q: number, r: number) {
        // 安全检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error(`❌ setupHexGridTouchEvents: 尝试为无效坐标(${q},${r})设置触摸事件`);
            return;
        }

        // 长按相关变量
        let isLongPressing = false;
        let longPressTimer = 0;
        let longPressCallback: Function = null;
        const LONG_PRESS_TIME = 1.0; // 1秒长按时间

        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, (_event: cc.Event.EventTouch) => {
            isLongPressing = true;
            longPressTimer = 0;

            // 开始长按检测
            longPressCallback = () => {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        this.onHexGridLongPress(q, r);
                        isLongPressing = false;
                        if (longPressCallback) {
                            this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            this.schedule(longPressCallback, 0.1);
        }, this);

        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                this.onHexGridClick(q, r, event);
            }

            isLongPressing = false;
            if (longPressCallback) {
                this.unschedule(longPressCallback);
            }
        }, this);

        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, (_event: cc.Event.EventTouch) => {
            isLongPressing = false;
            if (longPressCallback) {
                this.unschedule(longPressCallback);
            }
        }, this);

        // 添加Button组件以确保触摸响应
        let button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    }

    // 六边形格子点击事件 - 发送挖掘操作
    private onHexGridClick(q: number, r: number, _event?: cc.Event.EventTouch) {
       

        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);
            return;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn(`⚠️ 格子(${q}, ${r})已有玩家`);
            return;
        }

       

        // 发送挖掘操作事件 (action = 1)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 1  // 1 = 挖掘
        });
    }

    // 六边形格子长按事件 - 发送标记操作
    private onHexGridLongPress(q: number, r: number) {
    

        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);
            return;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn(`⚠️ 格子(${q}, ${r})已有玩家`);
            return;
        }

       

        // 发送标记操作事件 (action = 2)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 2  // 2 = 标记
        });
    }

    // 检查六边形坐标是否有效
    public isValidHexCoordinate(q: number, r: number): boolean {
        const key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    }

    // 在六边形格子上放置玩家预制体
    public placePlayerOnHexGrid(q: number, r: number, withFlag: boolean = false) {
        // 双重检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error(`❌ placePlayerOnHexGrid: 无效坐标(${q},${r})`);
            return;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        
        // 双重检查：确保格子为空
        if (!gridData || gridData.hasPlayer) {
            console.error(`❌ placePlayerOnHexGrid: 格子(${q},${r})已有玩家，不能重复放置`);
            return;
        }

        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }

        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }

        // 实例化玩家预制体
        let playerNode = cc.instantiate(this.playerGamePrefab);

        // 计算正确的位置（单人头像预制体，y轴+20）
        let correctPosition = this.getHexWorldPosition(q, r, true);
        playerNode.setPosition(correctPosition);

        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;

        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);

        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, q, r, withFlag, () => {
            // 头像加载完成的回调，播放生成动画
            this.playAvatarSpawnAnimation(playerNode);
        });

        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    }

    // 安全地添加玩家节点（处理Layout限制）
    private addPlayerNodeSafely(playerNode: cc.Node) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }

        // 检查棋盘节点是否有Layout组件
        let layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        } else {
            this.boardNode.addChild(playerNode);
        }
    }

    // 异步设置玩家头像（带回调）
    private setupPlayerAvatarAsync(playerNode: cc.Node, q: number, r: number, withFlag: boolean, onComplete: () => void) {
        // 查找PlayerGameController组件（使用类引用）
        let playerController = playerNode.getComponent(PlayerGameController);

        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                let avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }

                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            } else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }

            // 设置旗子节点的显示状态
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;

                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;

                    // 确保旗子节点的父节点也是可见的
                    let parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                }
            } else {
                console.warn(`⚠️ 找不到旗子节点 (${q},${r})`);
            }

            // 获取当前用户ID
            const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId || `hex_player_${q}_${r}`;

            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = currentUserId;

            // 创建用户数据并设置头像
            let userData = {
                userId: currentUserId,
                nickName: `玩家(${q},${r})`,
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            } as RoomUser;

            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);

                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(() => {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            } catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }

        } else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    }

    // 获取默认头像URL
    private getDefaultAvatarUrl(): string {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    }

    /**
     * 播放头像生成动画（由大变小，最终缩放为0.8）
     * @param playerNode 玩家节点
     */
    private playAvatarSpawnAnimation(playerNode: cc.Node) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }

        // 显示节点
        playerNode.active = true;

        // 设置最终缩放为0.8
        const finalScale = 0.8;
        // 设置初始缩放为1.2倍（比最终大一些，用于动画效果）
        const startScale = finalScale * 1.5;
        playerNode.setScale(startScale);

        // 使用cc.Tween创建由大变小的缩放动画，最终缩放到0.8
        cc.tween(playerNode)
            .to(0.3, { scaleX: finalScale, scaleY: finalScale }, { easing: 'backOut' })
            .start();
    }

    // 清除指定六边形格子的玩家
    public clearHexGridPlayer(q: number, r: number): boolean {
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        if (!gridData || !gridData.hasPlayer) {
            return false;
        }

        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }

        // 更新数据
        gridData.hasPlayer = false;

        return true;
    }

    // 清除所有玩家
    public clearAllPlayers() {
        let clearedCount = 0;

        // 1. 清理存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer && gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
                gridData.hasPlayer = false;
                clearedCount++;
            }
        });

        // 2. 清理棋盘上的其他玩家头像节点
        if (this.boardNode) {
            const children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组
            for (let i = 0; i < children.length; i++) {
                const child = children[i];
                if (child.name === "player_game_pfb") {
                    // 检查是否有PlayerGameController组件
                    const playerController = child.getComponent(PlayerGameController);
                    if (playerController) {
                        child.removeFromParent();
                        clearedCount++;
                    }
                }
            }
        }

        console.log(`清理了 ${clearedCount} 个六边形头像节点`);
    }

    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     * 为了与四边形棋盘控制器保持一致的接口
     */
    public clearAllPlayerNodes() {
        this.clearAllPlayers();
    }

    // 获取所有已放置玩家的六边形坐标
    public getAllPlayerHexCoordinates(): {q: number, r: number}[] {
        let coordinates: {q: number, r: number}[] = [];

        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer) {
                coordinates.push({q: gridData.q, r: gridData.r});
            }
        });

        return coordinates;
    }

    // 检查六边形格子是否为空
    public isHexGridEmpty(q: number, r: number): boolean {
        if (!this.isValidHexCoordinate(q, r)) {
            return false;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        return gridData ? !gridData.hasPlayer : false;
    }

    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    public resetGameScene() {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }

        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();

        // 显示所有小格子
        this.showAllHexGrids();

        // 重新初始化棋盘数据
        this.reinitializeHexBoardData();
    }

    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    private clearAllGameElements() {
        if (!this.boardNode) {
            return;
        }

        const childrenToRemove: cc.Node[] = [];

        // 遍历棋盘的所有子节点
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];
            const nodeName = child.name;

            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
        }

        // 移除找到的游戏元素
        childrenToRemove.forEach((child) => {
            child.removeFromParent();
        });
    }

    /**
     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除
     */
    private isGameElement(node: cc.Node, nodeName: string): boolean {
        // 绝对不清除的节点（六边形小格子）
        if (nodeName.startsWith("HexGrid_") || nodeName === "hexblock") {
            return false;
        }

        // 分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }

        // UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }

        // 明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }

        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            return true;
        }

        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }

        // 测试节点（Test_q_r 格式）
        if (nodeName.match(/^Test_-?\d+_-?\d+$/)) {
            return true;
        }

        // 玩家预制体（通过组件判断）
        if (node.getComponent(PlayerGameController)) {
            return true;
        }

        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }

        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }

        // 默认保留未知节点（保守策略）
        return false;
    }

    /**
     * 显示所有六边形小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    private showAllHexGrids() {
        if (!this.boardNode) {
            return;
        }

        // 遍历棋盘的所有子节点，找到小格子并显示
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 如果是六边形小格子节点
            if (child.name.startsWith("HexGrid_") || child.name === "hexblock") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();

                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;

                // 确保格子可以交互
                const button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    }

    /**
     * 隐藏指定位置的六边形小格子（点击时调用）
     */
    public hideHexGridAt(q: number, r: number) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`隐藏格子失败：坐标(${q}, ${r})无效`);
            return;
        }

        // 获取格子节点
        const key = this.getHexKey(q, r);
        const gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(() => {
                    gridNode.active = false;
                })
                .start();
        }
    }

    /**
     * 重新初始化六边形棋盘数据
     */
    private reinitializeHexBoardData() {
        // 重置hexGridData中的玩家状态
        this.hexGridData.forEach((gridData) => {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
    }

    /**
     * 获取六边形格子数据
     */
    public getHexGridData(q: number, r: number): HexGridData | null {
        const key = this.getHexKey(q, r);
        return this.hexGridData.get(key) || null;
    }

    /**
     * 批量放置玩家（用于从服务器同步数据）
     */
    public batchPlaceHexPlayers(coordinates: {q: number, r: number}[]) {
        coordinates.forEach(coord => {
            if (this.isValidHexCoordinate(coord.q, coord.r) && this.isHexGridEmpty(coord.q, coord.r)) {
                this.placePlayerOnHexGrid(coord.q, coord.r);
            }
        });
    }

    /**
     * 测试点击功能（调试用）
     */
    public testHexClick(q: number, r: number) {
        this.onHexGridClick(q, r);
    }

    /**
     * 获取棋盘状态信息（调试用）
     */
    public getHexBoardInfo() {
        let info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerHexCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size
        };

        return info;
    }

    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    public getHexGridCount(): number {
        return this.validHexCoords.length;
    }

    /**
     * 根据前端节点数量计算推荐的炸弹数量
     */
    public getRecommendedMineCount(): number {
        const gridCount = this.getHexGridCount();
        if (gridCount === 0) {
            return 13; // 默认值
        }

        // 约15%的格子是炸弹
        const mineCount = Math.floor(gridCount * 0.15);

        return Math.max(mineCount, 5); // 至少5个炸弹
    }

    /**
     * 测试六边形预制体位置计算是否正确
     */
    public testHexPositionCalculation() {
        console.log("🧪 测试六边形预制体位置计算（使用格子中心坐标）:");

        // 测试更新后的基准点坐标
        const testPoints = [
            { q: 0, r: 0, expected: { x: -300, y: -258 }, desc: "r=0行基准点(0,0)" },
            { q: 1, r: -1, expected: { x: -258, y: -184 }, desc: "r=-1行基准点(1,-1)" },
            { q: 1, r: -2, expected: { x: -300, y: -108 }, desc: "r=-2行基准点(1,-2)" },
            { q: 2, r: -3, expected: { x: -258, y: -36 }, desc: "r=-3行基准点(2,-3)" },
            { q: 2, r: -4, expected: { x: -300, y: 37 }, desc: "r=-4行基准点(2,-4)" },
            { q: 3, r: -5, expected: { x: -258, y: 110 }, desc: "r=-5行基准点(3,-5)" },
            { q: 3, r: -6, expected: { x: -300, y: 185 }, desc: "r=-6行基准点(3,-6)" },
            { q: 4, r: -7, expected: { x: -258, y: 260 }, desc: "r=-7行基准点(4,-7)" }
        ];

        let correctCount = 0;

        testPoints.forEach(point => {
            const calculated = this.getHexWorldPosition(point.q, point.r);
            const errorX = Math.abs(calculated.x - point.expected.x);
            const errorY = Math.abs(calculated.y - point.expected.y);
            const isCorrect = errorX < 2 && errorY < 2; // 允许2像素误差

            if (isCorrect) correctCount++;

            console.log(`  ${point.desc}(${point.q},${point.r}): 计算(${calculated.x.toFixed(1)}, ${calculated.y.toFixed(1)}) vs 期望(${point.expected.x}, ${point.expected.y}) 误差(${errorX.toFixed(1)}, ${errorY.toFixed(1)}) ${isCorrect ? '✅' : '❌'}`);
        });

        console.log(`📊 预制体位置测试结果: ${correctCount}/${testPoints.length} 个坐标点正确`);

        // 测试一些中间坐标
        console.log("\n🔍 测试中间坐标的计算结果:");
        const intermediatePoints = [
            // r=0行测试
            { q: 2, r: 0 }, { q: 3, r: 0 }, { q: 4, r: 0 }, { q: 5, r: 0 }, { q: 6, r: 0 },
            // r=-1行测试
            { q: 3, r: -1 }, { q: 4, r: -1 }, { q: 5, r: -1 }, { q: 6, r: -1 },
            // r=-2行测试
            { q: 3, r: -2 }, { q: 4, r: -2 }, { q: 5, r: -2 }, { q: 6, r: -2 }, { q: 7, r: -2 },
            // r=-3行测试
            { q: 4, r: -3 }, { q: 5, r: -3 }, { q: 6, r: -3 }, { q: 7, r: -3 }
        ];

        intermediatePoints.forEach(point => {
            const calculated = this.getHexWorldPosition(point.q, point.r);
            console.log(`  (${point.q},${point.r}): (${calculated.x.toFixed(1)}, ${calculated.y.toFixed(1)})`);
        });

        // 暴露到全局以便调试
        (window as any).testHexPositions = () => this.testHexPositionCalculation();
    }

    // ==================== NoticeActionDisplay 相关方法 ====================
    // 以下方法与第一张地图（方形地图）的逻辑完全一样，用于处理加分和掀开地图

    /**
     * 在指定六边形位置创建boom预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param isCurrentUser 是否是当前用户点到的雷
     */
    public createHexBoomPrefab(q: number, r: number, isCurrentUser: boolean = true) {
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }

        // 实例化boom预制体
        const boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";

        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        const position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);

        // 添加到棋盘
        this.boardNode.addChild(boomNode);

        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();

        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
    }

    /**
     * 在指定六边形位置创建biaoji预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     */
    public createHexBiaojiPrefab(q: number, r: number) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }

        // 实例化biaoji预制体
        const biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";

        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        const position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);

        // 添加到棋盘
        this.boardNode.addChild(biaojiNode);

        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    }

    /**
     * 更新指定六边形位置的neighborMines显示
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param neighborMines 周围地雷数量
     */
    public updateHexNeighborMinesDisplay(q: number, r: number, neighborMines: number) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }

        // 直接使用boom数字预制体
        this.createHexNumberPrefab(q, r, neighborMines);
    }

    /**
     * 创建六边形数字预制体（boom1, boom2, ...）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param number 数字
     */
    private createHexNumberPrefab(q: number, r: number, number: number) {
        // 根据数字选择对应的预制体
        let prefab: cc.Prefab = null;
        switch (number) {
            case 1: prefab = this.boom1Prefab; break;
            case 2: prefab = this.boom2Prefab; break;
            case 3: prefab = this.boom3Prefab; break;
            case 4: prefab = this.boom4Prefab; break;
            case 5: prefab = this.boom5Prefab; break;
            case 6: prefab = this.boom6Prefab; break;
            case 7: prefab = this.boom7Prefab; break;
            case 8: prefab = this.boom8Prefab; break;
            default:
                console.error(`不支持的数字: ${number}`);
                return;
        }

        if (!prefab) {
            console.error(`boom${number}Prefab 预制体未设置，请在编辑器中挂载`);
            return;
        }

        // 实例化数字预制体
        const numberNode = cc.instantiate(prefab);
        numberNode.name = `HexBoom${number}`;

        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        const position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);

        // 添加到棋盘
        this.boardNode.addChild(numberNode);

        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    }

    /**
     * 播放棋盘震动动画（当前用户点到雷时）
     */
    private playBoardShakeAnimation() {
        if (!this.boardNode) {
            return;
        }

        const originalPosition = this.boardNode.getPosition();
        const shakeDistance = 10;

        cc.tween(this.boardNode)
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x })
            .start();
    }

    // ==================== 头像生命周期管理 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于管理头像预制体的生命周期

    /**
     * 让所有六边形头像消失（和第一张地图的hideAvatarsAtPosition逻辑一样）
     * @param onComplete 完成回调
     */
    public hideAllHexAvatars(onComplete: () => void) {
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理六边形头像");
            onComplete();
            return;
        }

        // 收集所有头像节点（参考第一张地图的逻辑）
        const avatarNodes: cc.Node[] = [];

        // 方法1: 收集存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer && gridData.playerNode) {
                avatarNodes.push(gridData.playerNode);
            }
        });

        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 检查是否是玩家预制体（通过组件判断）
            const playerController = child.getComponent(PlayerGameController);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }

        console.log(`准备让 ${avatarNodes.length} 个六边形头像消失`);

        // 如果没有头像，直接执行回调
        if (avatarNodes.length === 0) {
            this.clearAllMyHexAvatarReferences();
            onComplete();
            return;
        }

        let completedCount = 0;
        const totalCount = avatarNodes.length;

        // 为每个头像播放消失动画（和第一张地图完全一样）
        avatarNodes.forEach((avatarNode) => {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(() => {
                    // 动画完成后移除节点
                    avatarNode.removeFromParent();
                    completedCount++;

                    // 所有头像都消失完成后，执行回调
                    if (completedCount >= totalCount) {
                        // 清除所有自己头像的引用
                        this.clearAllMyHexAvatarReferences();
                        onComplete();
                    }
                })
                .start();
        });
    }

    /**
     * 清除所有自己六边形头像的引用（和第一张地图的clearAllMyAvatarReferences逻辑一样）
     */
    private clearAllMyHexAvatarReferences() {
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
        console.log("已清除所有六边形头像引用");
    }

    // ==================== 加分逻辑相关方法 ====================
    // 以下方法与第一张地图的加分逻辑完全一样

    /**
     * 在指定六边形位置的玩家节点上显示分数
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    public showScoreOnHexPlayerNode(q: number, r: number, score: number, showPlusOne: boolean) {
        // 查找该位置的玩家节点
        const playerNode = this.findHexPlayerNodeAtPosition(q, r);
        if (!playerNode) {
            // 在NoticeActionDisplay流程中，头像会被清理，找不到节点是正常的
            console.log(`在六边形位置(${q}, ${r})找不到玩家节点，跳过头像分数显示（正常现象）`);
            return;
        }

        // 获取PlayerGameController组件
        const playerController = playerNode.getComponent(PlayerGameController);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }

        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, 1, () => {
                this.scheduleOnce(() => {
                    this.showScoreAnimationOnHexNode(playerController, score, null);
                }, 1.0);
            });
        } else {
            // 只显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, score, null);
        }
    }

    /**
     * 查找指定六边形位置的玩家节点
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @returns 玩家节点或null
     */
    private findHexPlayerNodeAtPosition(q: number, r: number): cc.Node | null {
        // 方法1: 从hexGridData中查找（自己的头像）
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode) {
            return gridData.playerNode;
        }

        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }

        // 遍历棋盘上的所有子节点，查找player_game_pfb
        const children = this.boardNode.children;
        for (let i = 0; i < children.length; i++) {
            const child = children[i];
            if (child.name === "player_game_pfb") {
                // 检查位置是否匹配（允许一定的误差）
                const expectedPos = this.getHexWorldPosition(q, r, true);
                const actualPos = child.getPosition();
                const distance = expectedPos.sub(actualPos).mag();
                if (distance < 10) { // 10像素误差范围内
                    return child;
                }
            }
        }

        return null;
    }

    /**
     * 在六边形节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    private showScoreAnimationOnHexNode(playerController: any, score: number, onComplete: (() => void) | null) {
        // 调用PlayerGameController的showAddScore方法
        if (playerController && typeof playerController.showAddScore === 'function') {
            playerController.showAddScore(score);
        }

        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    }

    /**
     * 显示玩家游戏加减分效果（完全复制四边形棋盘控制器的逻辑）
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    public showHexPlayerGameScore(userId: string, score: number) {
        const currentUserId = this.getCurrentHexUserId();
        let foundPlayer = false;

        // 1. 如果是当前用户，查找自己的玩家节点（存储在hexGridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentHexUser(score);
        } else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherHexUser(userId, score);
        }

        if (!foundPlayer) {
            console.warn(`未找到用户 ${userId} 的六边形头像节点来显示分数效果`);
        }
    }

    /**
     * 获取当前用户ID（复制四边形棋盘控制器的方法）
     */
    private getCurrentHexUserId(): string {
        return GlobalBean.GetInstance().loginData?.userInfo?.userId || "";
    }

    /**
     * 为当前用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    private showScoreForCurrentHexUser(score: number): boolean {
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer && gridData.playerNode) {
                let playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                                     gridData.playerNode.getComponent("PlayerGameController ");

                if (playerController) {
                    this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                }
            }
        });
        return false;
    }

    /**
     * 为其他用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    private showScoreForOtherHexUser(userId: string, score: number): boolean {
        if (!this.boardNode) {
            return false;
        }

        // 遍历棋盘上的所有玩家头像节点
        return this.findHexPlayerNodeByUserId(userId, score);
    }

    /**
     * 根据userId查找对应的玩家节点（复制四边形棋盘控制器的逻辑）
     */
    private findHexPlayerNodeByUserId(userId: string, score: number): boolean {
        if (!this.boardNode) {
            console.warn(`棋盘节点不存在，无法查找用户 ${userId} 的头像`);
            return false;
        }

        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 尝试多种方式获取PlayerGameController组件
            let playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController ");  // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                const components = child.getComponents(cc.Component);
                playerController = components.find(comp =>
                    comp.constructor.name === 'PlayerGameController' ||
                    comp.constructor.name === 'PlayerGameController '
                );
            }

            const storedUserId = child['userId'];

            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                } else {
                    // 找到匹配的用户ID但没有组件
                    console.warn(`⚠️ 找到用户 ${userId} 的节点但没有PlayerGameController组件`);
                    return false;  // 找到节点但没有组件，返回false
                }
            }
        }

        console.warn(`❌ 未找到用户 ${userId} 的六边形头像节点`);
        return false;
    }

    /**
     * 在PlayerController上显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    private showScoreOnHexPlayerController(playerController: any, score: number) {
        // 临时提升节点层级，避免被其他头像遮挡
        const playerNode = playerController.node;
        const originalSiblingIndex = playerNode.getSiblingIndex();

        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);

        if (score > 0) {
            playerController.showAddScore(score);
        } else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }

        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(() => {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.0); // 2秒后恢复，根据分数动画时长调整
    }

    /**
     * 查找指定用户ID的所有六边形头像节点
     * @param userId 用户ID
     * @returns 头像节点数组
     */
    private findAllHexPlayerNodesByUserId(userId: string): cc.Node[] {
        const playerNodes: cc.Node[] = [];

        if (!this.boardNode) {
            return playerNodes;
        }

        // 遍历棋盘上的所有子节点
        const children = this.boardNode.children;
        for (let i = 0; i < children.length; i++) {
            const child = children[i];

            // 检查是否是玩家预制体（通过组件判断）
            const playerController = child.getComponent(PlayerGameController);
            if (playerController) {
                // 检查是否是指定用户的头像（使用存储在节点上的userId）
                const storedUserId = child['userId'];
                if (storedUserId === userId) {
                    playerNodes.push(child);
                }
            }
        }

        // 也检查存储在hexGridData中的玩家节点
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer && gridData.playerNode) {
                const playerController = gridData.playerNode.getComponent(PlayerGameController);
                const storedUserId = gridData.playerNode['userId'];
                if (playerController && storedUserId === userId) {
                    // 避免重复添加
                    if (!playerNodes.includes(gridData.playerNode)) {
                        playerNodes.push(gridData.playerNode);
                    }
                }
            }
        });

        return playerNodes;
    }

    // ==================== 其他玩家头像生成 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于生成其他玩家的头像

    /**
     * 在指定六边形位置显示其他玩家的操作（和第一张地图的displayOtherPlayersAtPosition逻辑一样）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 该位置的其他玩家操作列表
     */
    public displayOtherPlayersAtHexPosition(q: number, r: number, actions: PlayerActionDisplay[]) {
        if (!this.isValidHexCoordinate(q, r) || !actions || actions.length === 0) {
            console.warn(`无效参数: (${q}, ${r}), actions: ${actions?.length || 0}`);
            return;
        }

        // 检查该位置是否已经有自己的头像
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        if (gridData && gridData.hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingHexGrid(q, r, actions);
            }
        } else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyHexGrid(q, r, actions);
        }
    }

    /**
     * 在已有自己头像的六边形格子上添加其他玩家头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    private addOtherPlayersToExistingHexGrid(q: number, r: number, actions: PlayerActionDisplay[]) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        const totalPlayers = 1 + actions.length;
        const positions = this.getHexPlayerPositions(totalPlayers);

        // 第一步：调整自己的头像位置和缩放
        const myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyHexAvatarPosition(q, r, myPosition, actions);

        // 第二步：从第二个位置开始放置其他玩家
        for (let i = 0; i < actions.length; i++) {
            const action = actions[i];
            const position = positions[i + 1]; // 跳过第一个位置（自己的位置）

            // 创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    }

    /**
     * 在空六边形格子上添加其他玩家头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    private addOtherPlayersToEmptyHexGrid(q: number, r: number, actions: PlayerActionDisplay[]) {
        const totalPlayers = actions.length; // 空格子上只有其他玩家
        const positions = this.getHexPlayerPositions(totalPlayers);

        for (let i = 0; i < actions.length; i++) {
            const action = actions[i];
            const position = positions[i];

            // 创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    }

    /**
     * 获取六边形多人布局的位置信息（和第一张地图的getPlayerPositions逻辑一样）
     * @param playerCount 玩家数量
     * @returns 位置数组
     */
    private getHexPlayerPositions(playerCount: number): {x: number, y: number, scale: number}[] {
        // 复用方形地图的多人布局逻辑
        const positions = [
            // 1人：中心位置
            [{x: 0, y: 0, scale: 0.8}],
            // 2人：左右分布
            [{x: -15, y: 0, scale: 0.7}, {x: 15, y: 0, scale: 0.7}],
            // 3人：三角形分布
            [{x: 0, y: 10, scale: 0.6}, {x: -12, y: -8, scale: 0.6}, {x: 12, y: -8, scale: 0.6}],
            // 4人：四角分布
            [{x: -10, y: 8, scale: 0.5}, {x: 10, y: 8, scale: 0.5}, {x: -10, y: -8, scale: 0.5}, {x: 10, y: -8, scale: 0.5}]
        ];

        return positions[Math.min(playerCount, 4) - 1] || positions[3];
    }

    /**
     * 调整自己的六边形头像位置和缩放（当多人在同一格子时）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    private adjustMyHexAvatarPosition(q: number, r: number, position: {x: number, y: number, scale: number}, actions: PlayerActionDisplay[]) {
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        // 查找自己的头像节点
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            console.warn(`在六边形位置(${q}, ${r})找不到自己的头像节点`);
            return;
        }

        const myPlayerNode = gridData.playerNode;

        // 计算该格子的总人数（自己 + 其他玩家）
        const totalPlayers = 1 + (actions ? actions.length : 0);

        // 计算基础位置
        const basePosition = this.getHexWorldPosition(q, r, true);

        // 计算新的最终位置
        const newPosition = cc.v2(
            basePosition.x + position.x,
            basePosition.y + position.y
        );

        // 播放平滑移动和缩小动画
        this.playHexAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    }

    /**
     * 创建其他玩家在六边形位置的头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param action 玩家操作数据
     * @param position 位置和缩放信息
     * @param totalPlayers 总玩家数
     */
    private createOtherPlayerAtHexPosition(q: number, r: number, action: PlayerActionDisplay, position: {x: number, y: number, scale: number}, totalPlayers: number) {
        if (!this.playerGamePrefab || !this.boardNode) {
            console.error("❌ 预制体或棋盘节点未设置！");
            return;
        }

        // 实例化玩家预制体
        let playerNode = cc.instantiate(this.playerGamePrefab);

        // 计算基础位置
        const basePosition = this.getHexWorldPosition(q, r, true);

        // 计算最终位置
        const finalPosition = cc.v2(
            basePosition.x + position.x,
            basePosition.y + position.y
        );

        playerNode.setPosition(finalPosition);
        playerNode.setScale(position.scale);

        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;

        // 添加到棋盘
        this.addPlayerNodeSafely(playerNode);

        // 设置其他玩家的头像和数据
        this.setupOtherPlayerHexAvatar(playerNode, action, () => {
            // 头像加载完成的回调，播放生成动画
            this.playAvatarSpawnAnimation(playerNode);
        });
    }

    /**
     * 设置其他玩家的六边形头像和数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    private setupOtherPlayerHexAvatar(playerNode: cc.Node, action: PlayerActionDisplay, onComplete: () => void) {
        // 查找PlayerGameController组件
        let playerController = playerNode.getComponent(PlayerGameController);

        if (playerController) {
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = action.userId;

            // 设置旗子节点的显示状态
            const withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
            }

            // 获取真实的用户数据（和第一张地图逻辑一样）
            let realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn(`找不到用户 ${action.userId} 的真实数据，使用默认数据`);
                // 使用默认数据作为备选
                realUserData = {
                    userId: action.userId,
                    nickName: `玩家${action.userId}`,
                    avatar: this.getDefaultAvatarUrl(),
                    score: 0,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    rank: 0
                } as RoomUser;
            }

            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(realUserData);

                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(() => {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            } catch (error) {
                console.error("设置其他玩家头像数据失败:", error);
                onComplete();
            }
        } else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    }

    /**
     * 获取其他玩家的头像URL
     * @param userId 用户ID
     * @returns 头像URL
     */
    private getOtherPlayerAvatarUrl(userId: string): string {
        // 这里可以根据userId获取真实的头像URL
        // 暂时使用默认头像
        return this.getDefaultAvatarUrl();
    }



    /**
     * 播放六边形头像调整动画
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    private playHexAvatarAdjustAnimation(playerNode: cc.Node, newPosition: cc.Vec2, newScale: number) {
        // 播放平滑移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
                x: newPosition.x,
                y: newPosition.y,
                scaleX: newScale,
                scaleY: newScale
            }, { easing: 'sineOut' })
            .start();
    }

    /**
     * 从GlobalBean中获取真实的用户数据（和第一张地图逻辑完全一样）
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    private getRealUserData(userId: string): RoomUser | null {
        try {
            if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }

            const users = GlobalBean.GetInstance().noticeStartGame.users;
            const user = users.find((u: RoomUser) => u.userId === userId);

            if (user) {
                return user;
            } else {
                console.warn(`未找到用户 ${userId} 的数据`);
                return null;
            }
        } catch (error) {
            console.error(`获取用户数据时出错: ${error}`);
            return null;
        }
    }
}
